const mongoose = require('mongoose');

const messageSchema = new mongoose.Schema({
    // Message identification
    id: {
        type: String,
        required: true,
        unique: true,
        index: true
    },
    
    // Message content
    msg: {
        type: String,
        required: true,
        maxlength: 2000
    },
    link: {
        type: String,
        default: null,
        maxlength: 500
    },
    
    // Message relationships
    bid: {
        type: String,
        default: null, // Reply to message ID
        index: true
    },
    replyTo: {
        messageId: String,
        userId: String,
        userTopic: String,
        preview: String
    },
    
    // User information
    userId: {
        type: String,
        required: true,
        index: true
    },
    userTopic: {
        type: String,
        required: true
    },
    userPic: {
        type: String,
        default: 'pic.webp'
    },
    userBg: {
        type: String,
        default: '#ffffff'
    },
    userColor: {
        type: String,
        default: '#000000'
    },
    userIco: {
        type: String,
        default: ''
    },
    
    // Room information
    roomId: {
        type: String,
        required: true,
        index: true
    },
    
    // Message metadata
    type: {
        type: String,
        enum: ['text', 'image', 'audio', 'system', 'emoji'],
        default: 'text'
    },
    isEdited: {
        type: Boolean,
        default: false
    },
    editedAt: {
        type: Date,
        default: null
    },
    
    // Moderation
    isDeleted: {
        type: Boolean,
        default: false,
        index: true
    },
    deletedBy: {
        type: String,
        default: null
    },
    deletedAt: {
        type: Date,
        default: null
    },
    deleteReason: {
        type: String,
        default: null
    },
    
    // Message flags
    isHidden: {
        type: Boolean,
        default: false
    },
    isPinned: {
        type: Boolean,
        default: false
    },
    isSystem: {
        type: Boolean,
        default: false
    },
    
    // Reactions and interactions
    reactions: [{
        emoji: String,
        users: [String], // User IDs who reacted
        count: {
            type: Number,
            default: 0
        }
    }],
    
    // Message statistics
    views: {
        type: Number,
        default: 0
    },
    reports: [{
        userId: String,
        reason: String,
        reportedAt: {
            type: Date,
            default: Date.now
        }
    }],
    
    // Spam detection
    spamScore: {
        type: Number,
        default: 0,
        min: 0,
        max: 100
    },
    
    // Message formatting
    formatting: {
        bold: {
            type: Boolean,
            default: false
        },
        italic: {
            type: Boolean,
            default: false
        },
        color: {
            type: String,
            default: null
        },
        size: {
            type: Number,
            default: 14,
            min: 8,
            max: 24
        }
    },
    
    // Attachments
    attachments: [{
        type: {
            type: String,
            enum: ['image', 'audio', 'file']
        },
        url: String,
        filename: String,
        size: Number,
        mimeType: String
    }],
    
    // IP and fingerprint for moderation
    ip: {
        type: String,
        select: false
    },
    fingerprint: {
        type: String,
        select: false
    }
}, {
    timestamps: true,
    toJSON: {
        transform: function(doc, ret) {
            delete ret._id;
            delete ret.__v;
            delete ret.ip;
            delete ret.fingerprint;
            if (ret.isDeleted) {
                ret.msg = '[رسالة محذوفة]';
                ret.link = null;
                ret.attachments = [];
            }
            return ret;
        }
    }
});

// Indexes for performance
messageSchema.index({ roomId: 1, createdAt: -1 });
messageSchema.index({ userId: 1, createdAt: -1 });
messageSchema.index({ bid: 1 });
messageSchema.index({ isDeleted: 1, createdAt: -1 });
messageSchema.index({ isPinned: 1, roomId: 1 });

// Methods
messageSchema.methods.toClientFormat = function() {
    return {
        id: this.id,
        msg: this.isDeleted ? '[رسالة محذوفة]' : this.msg,
        link: this.isDeleted ? null : this.link,
        bid: this.bid,
        userId: this.userId,
        userTopic: this.userTopic,
        userPic: this.userPic,
        userBg: this.userBg,
        userColor: this.userColor,
        userIco: this.userIco,
        roomId: this.roomId,
        type: this.type,
        isEdited: this.isEdited,
        reactions: this.reactions,
        isPinned: this.isPinned,
        createdAt: this.createdAt.getTime(),
        replyTo: this.replyTo,
        attachments: this.isDeleted ? [] : this.attachments
    };
};

messageSchema.methods.addReaction = function(emoji, userId) {
    const existingReaction = this.reactions.find(r => r.emoji === emoji);
    
    if (existingReaction) {
        if (!existingReaction.users.includes(userId)) {
            existingReaction.users.push(userId);
            existingReaction.count = existingReaction.users.length;
        }
    } else {
        this.reactions.push({
            emoji: emoji,
            users: [userId],
            count: 1
        });
    }
    
    return this.save();
};

messageSchema.methods.removeReaction = function(emoji, userId) {
    const reactionIndex = this.reactions.findIndex(r => r.emoji === emoji);
    
    if (reactionIndex !== -1) {
        const reaction = this.reactions[reactionIndex];
        const userIndex = reaction.users.indexOf(userId);
        
        if (userIndex !== -1) {
            reaction.users.splice(userIndex, 1);
            reaction.count = reaction.users.length;
            
            if (reaction.count === 0) {
                this.reactions.splice(reactionIndex, 1);
            }
        }
    }
    
    return this.save();
};

// Static methods
messageSchema.statics.findRoomMessages = function(roomId, limit = 50, before = null) {
    const query = { roomId, isDeleted: false };
    if (before) {
        query.createdAt = { $lt: new Date(before) };
    }
    
    return this.find(query)
        .sort({ createdAt: -1 })
        .limit(limit);
};

messageSchema.statics.findUserMessages = function(userId, limit = 100) {
    return this.find({ userId, isDeleted: false })
        .sort({ createdAt: -1 })
        .limit(limit);
};

messageSchema.statics.cleanupOldMessages = function(days = 30) {
    const cutoffDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000);
    return this.deleteMany({
        createdAt: { $lt: cutoffDate },
        isPinned: false,
        isSystem: false
    });
};

module.exports = mongoose.model('Message', messageSchema);
