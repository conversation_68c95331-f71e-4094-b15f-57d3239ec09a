const { v4: uuidv4 } = require('uuid');

/**
 * Generate unique user ID
 */
function generateUserId() {
    return uuidv4();
}

/**
 * Generate unique room ID
 */
function generateRoomId() {
    return uuidv4();
}

/**
 * Generate unique message ID
 */
function generateMessageId() {
    return uuidv4();
}

/**
 * Generate random guest name
 */
function generateGuestName() {
    const adjectives = [
        'زائر', 'ضيف', 'عضو', 'مستخدم', 'شخص'
    ];
    const numbers = Math.floor(Math.random() * 9999) + 1;
    const adjective = adjectives[Math.floor(Math.random() * adjectives.length)];
    
    return `${adjective}_${numbers}`;
}

/**
 * Sanitize user input
 */
function sanitizeInput(input) {
    if (typeof input !== 'string') {
        return '';
    }
    
    return input
        .trim()
        .replace(/[<>]/g, '') // Remove potential HTML tags
        .replace(/javascript:/gi, '') // Remove javascript: URLs
        .replace(/on\w+=/gi, '') // Remove event handlers
        .substring(0, 2000); // Limit length
}

/**
 * Validate email format
 */
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

/**
 * Validate username format
 */
function isValidUsername(username) {
    if (!username || typeof username !== 'string') {
        return false;
    }
    
    // Username should be 3-30 characters, alphanumeric and underscores
    const usernameRegex = /^[a-zA-Z0-9_\u0600-\u06FF]{3,30}$/;
    return usernameRegex.test(username);
}

/**
 * Validate room name format
 */
function isValidRoomName(roomName) {
    if (!roomName || typeof roomName !== 'string') {
        return false;
    }
    
    // Room name should be 3-50 characters
    return roomName.trim().length >= 3 && roomName.trim().length <= 50;
}

/**
 * Format timestamp for display
 */
function formatTimestamp(timestamp) {
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now - date;
    
    // Less than 1 minute
    if (diff < 60000) {
        return 'الآن';
    }
    
    // Less than 1 hour
    if (diff < 3600000) {
        const minutes = Math.floor(diff / 60000);
        return `منذ ${minutes} دقيقة`;
    }
    
    // Less than 1 day
    if (diff < 86400000) {
        const hours = Math.floor(diff / 3600000);
        return `منذ ${hours} ساعة`;
    }
    
    // More than 1 day
    const days = Math.floor(diff / 86400000);
    return `منذ ${days} يوم`;
}

/**
 * Generate color from string (for user colors)
 */
function generateColorFromString(str) {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
        hash = str.charCodeAt(i) + ((hash << 5) - hash);
    }
    
    const hue = Math.abs(hash) % 360;
    return `hsl(${hue}, 70%, 50%)`;
}

/**
 * Escape HTML entities
 */
function escapeHtml(text) {
    const map = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#039;'
    };
    
    return text.replace(/[&<>"']/g, (m) => map[m]);
}

/**
 * Parse mentions in message
 */
function parseMentions(message, users) {
    if (!message || !users) {
        return message;
    }
    
    // Find @username patterns
    const mentionRegex = /@(\w+)/g;
    
    return message.replace(mentionRegex, (match, username) => {
        const user = users.find(u => u.topic.toLowerCase() === username.toLowerCase());
        if (user) {
            return `<span class="mention" data-user-id="${user.id}">@${user.topic}</span>`;
        }
        return match;
    });
}

/**
 * Extract URLs from message
 */
function extractUrls(message) {
    const urlRegex = /(https?:\/\/[^\s]+)/g;
    return message.match(urlRegex) || [];
}

/**
 * Check if message contains bad words
 */
function containsBadWords(message) {
    const badWords = [
        // Add Arabic and English bad words here
        'spam', 'scam', 'hack'
    ];
    
    const lowerMessage = message.toLowerCase();
    return badWords.some(word => lowerMessage.includes(word));
}

/**
 * Calculate message similarity (for spam detection)
 */
function calculateSimilarity(str1, str2) {
    const longer = str1.length > str2.length ? str1 : str2;
    const shorter = str1.length > str2.length ? str2 : str1;
    
    if (longer.length === 0) {
        return 1.0;
    }
    
    const editDistance = levenshteinDistance(longer, shorter);
    return (longer.length - editDistance) / longer.length;
}

/**
 * Levenshtein distance calculation
 */
function levenshteinDistance(str1, str2) {
    const matrix = [];
    
    for (let i = 0; i <= str2.length; i++) {
        matrix[i] = [i];
    }
    
    for (let j = 0; j <= str1.length; j++) {
        matrix[0][j] = j;
    }
    
    for (let i = 1; i <= str2.length; i++) {
        for (let j = 1; j <= str1.length; j++) {
            if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
                matrix[i][j] = matrix[i - 1][j - 1];
            } else {
                matrix[i][j] = Math.min(
                    matrix[i - 1][j - 1] + 1,
                    matrix[i][j - 1] + 1,
                    matrix[i - 1][j] + 1
                );
            }
        }
    }
    
    return matrix[str2.length][str1.length];
}

/**
 * Generate random hex color
 */
function generateRandomColor() {
    const colors = [
        '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
        '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
    ];
    
    return colors[Math.floor(Math.random() * colors.length)];
}

/**
 * Validate hex color
 */
function isValidHexColor(color) {
    const hexRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
    return hexRegex.test(color);
}

/**
 * Get client IP from socket
 */
function getClientIP(socket) {
    return socket.handshake.headers['x-forwarded-for'] || 
           socket.handshake.headers['x-real-ip'] || 
           socket.handshake.address || 
           socket.conn.remoteAddress;
}

/**
 * Get user agent from socket
 */
function getUserAgent(socket) {
    return socket.handshake.headers['user-agent'] || 'Unknown';
}

/**
 * Check if user is on mobile device
 */
function isMobileDevice(userAgent) {
    const mobileRegex = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i;
    return mobileRegex.test(userAgent);
}

/**
 * Generate session ID
 */
function generateSessionId() {
    return uuidv4() + '-' + Date.now();
}

/**
 * Validate session ID format
 */
function isValidSessionId(sessionId) {
    const sessionRegex = /^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}-\d+$/;
    return sessionRegex.test(sessionId);
}

/**
 * Convert bytes to human readable format
 */
function formatBytes(bytes, decimals = 2) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

/**
 * Deep clone object
 */
function deepClone(obj) {
    if (obj === null || typeof obj !== 'object') {
        return obj;
    }
    
    if (obj instanceof Date) {
        return new Date(obj.getTime());
    }
    
    if (obj instanceof Array) {
        return obj.map(item => deepClone(item));
    }
    
    if (typeof obj === 'object') {
        const clonedObj = {};
        for (const key in obj) {
            if (obj.hasOwnProperty(key)) {
                clonedObj[key] = deepClone(obj[key]);
            }
        }
        return clonedObj;
    }
}

module.exports = {
    generateUserId,
    generateRoomId,
    generateMessageId,
    generateGuestName,
    sanitizeInput,
    isValidEmail,
    isValidUsername,
    isValidRoomName,
    formatTimestamp,
    generateColorFromString,
    escapeHtml,
    parseMentions,
    extractUrls,
    containsBadWords,
    calculateSimilarity,
    levenshteinDistance,
    generateRandomColor,
    isValidHexColor,
    getClientIP,
    getUserAgent,
    isMobileDevice,
    generateSessionId,
    isValidSessionId,
    formatBytes,
    deepClone
};
