<!DOCTYPE HTML>
<html lang="ar" hreflang="ar-sa" style="height: 100%;"> 
<head>   
  <meta content="text/html; charset=utf-8" http-equiv="Content-Type">
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=Edge">
  <meta property="og:title" content="شات الجوال | دردشة الجوال | شات جوال | دردشة جوال | شات الجوال للجوال">
  <meta property="og:description" content="شات الجوال.شات جوال,شات كتابي,شات للجوال,شات جوال الخليج, دردشة الجوال ، شات الجوال الكتابي ، شات جوال ، الجوال,شات دردشة جوال ، شات جوال للجوال ، شات الجوال الصوتي ، شات دردشة الجوال ، شات دردشة جوال ، شات الجوال الصوتي ، شات الجوال العربي ، شات الجوال الخليج شات دردشة الجوال">
  <meta property="og:image" content="prv1.webp"> 
  <link rel="icon" type="image/x-icon" href="favicon.ico">
  <meta name="google" value="notranslate">
  <meta name="HandheldFriendly" content="True">
  <meta name="viewport" content="width=400,maximum-scale=5">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="mobile-web-app-capable" content="yes">
  <title>شات الجوال | دردشة الجوال | شات جوال | دردشة جوال | شات الجوال للجوال</title>
  <meta name="description" content="شات الجوال.شات جوال,شات كتابي,شات للجوال,شات جوال الخليج, دردشة الجوال ، شات الجوال الكتابي ، شات جوال ، الجوال,شات دردشة جوال ، شات جوال للجوال ، شات الجوال الصوتي ، شات دردشة الجوال ، شات دردشة جوال ، شات الجوال الصوتي ، شات الجوال العربي ، شات الجوال الخليج شات دردشة الجوال">
  <meta content="شات الجوال.شات جوال,شات كتابي,شات للجوال,شات جوال الخليج, دردشة الجوال ، شات الجوال الكتابي ، شات جوال ، الجوال,شات دردشة جوال ، شات جوال للجوال ، شات الجوال الصوتي ، شات دردشة الجوال ، شات دردشة جوال ، شات الجوال الصوتي ، شات الجوال العربي ، شات الجوال الخليج شات دردشة الجوال" name="keywords">  
   <style>
 body{margin:0}article,aside,details,figcaption,figure,footer,header,hgroup,main,menu,nav,section,summary{display:block}audio,canvas,progress,video{display:inline-block;vertical-align:baseline}audio:not([controls]){display:none;height:0}[hidden],template{display:none}a{background-color:transparent}a:active,a:hover{outline:0}abbr[title]{border-bottom:none;text-decoration:underline;text-decoration:underline dotted}b,strong{font-weight:bold}dfn{font-style:italic}h1{font-size:2em;margin:0.67em 0}mark{background:#ff0;color:#000}small{font-size:80%}sub,sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}sup{top:-0.5em}sub{bottom:-0.25em}img{border:0;vertical-align: middle;}svg:not(:root){overflow:hidden}figure{margin:1em 40px}hr{-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box;height:0}pre{overflow:auto}code,kbd,pre,samp{font-family:monospace, monospace;font-size:1em}button,input,optgroup,select,textarea{color:inherit;font:inherit;margin:0}button{overflow:visible}button,select{text-transform:none}button,html input[type="button"],input[type="reset"],input[type="submit"]{-webkit-appearance:button;cursor:pointer}button[disabled],html input[disabled]{cursor:default}button::-moz-focus-inner,input::-moz-focus-inner{border:0;padding:0}input{line-height:normal}input[type="checkbox"],input[type="radio"]{-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;padding:0}input[type="number"]::-webkit-inner-spin-button,input[type="number"]::-webkit-outer-spin-button{height:auto}input[type="search"]{-webkit-appearance:textfield;-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box}input[type="search"]::-webkit-search-cancel-button,input[type="search"]::-webkit-search-decoration{-webkit-appearance:none}fieldset{border:1px solid #c0c0c0;margin:0 2px;padding:0.35em 0.625em 0.75em}legend{border:0;padding:0}textarea{overflow:auto}optgroup{font-weight:bold}table{border-collapse:collapse;border-spacing:0}td,th{padding:0}*{-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box}*:before,*:after{-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box}html{font-size:10px;-webkit-tap-highlight-color:rgba(0,0,0,0)}input,button,select,textarea{font-family:inherit;font-size:inherit;line-height:inherit}a{color:#337ab7;text-decoration:none}a:hover,a:focus{color:#23527c;text-decoration:underline}a:focus{outline:5px auto -webkit-focus-ring-color;outline-offset:-2px}figure{margin:0}.img-responsive{display:block;max-width:100%;height:auto}.img-rounded{border-radius:6px}.img-thumbnail{padding:4px;line-height:1.42857143;background-color:#fff;border:1px solid #ddd;border-radius:4px;-webkit-transition:all .2s ease-in-out;-o-transition:all .2s ease-in-out;transition:all .2s ease-in-out;display:inline-block;max-width:100%;height:auto}.img-circle{border-radius:50%}hr{margin-top:20px;margin-bottom:20px;border:0;border-top:1px solid #eee}.sr-only{position:absolute;width:1px;height:1px;padding:0;margin:-1px;overflow:hidden;clip:rect(0, 0, 0, 0);border:0}.sr-only-focusable:active,.sr-only-focusable:focus{position:static;width:auto;height:auto;margin:0;overflow:visible;clip:auto}[role="button"]{cursor:pointer}fieldset{min-width:0;padding:0;margin:0;border:0}legend{display:block;width:100%;padding:0;margin-bottom:20px;font-size:21px;line-height:inherit;color:#333;border:0;border-bottom:1px solid #e5e5e5}label{display:inline-block;max-width:100%;margin-bottom:5px;font-weight:700}input[type="search"]{-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;-webkit-appearance:none;appearance:none}input[type="radio"],input[type="checkbox"]{margin:4px 0 0;margin-top:1px \9;line-height:normal}input[type="radio"][disabled],input[type="checkbox"][disabled],input[type="radio"].disabled,input[type="checkbox"].disabled,fieldset[disabled] input[type="radio"],fieldset[disabled] input[type="checkbox"]{cursor:not-allowed}input[type="file"]{display:block}input[type="range"]{display:block;width:100%}select[multiple],select[size]{height:auto}input[type="file"]:focus,input[type="radio"]:focus,input[type="checkbox"]:focus{outline:5px auto -webkit-focus-ring-color;outline-offset:-2px}output{display:block;padding-top:7px;font-size:14px;line-height:1.42857143;color:#555}.form-control{display:block;width:100%;height:34px;padding:6px 12px;font-size:14px;line-height:1.42857143;color:#555;background-color:#fff;background-image:none;border:1px solid #ccc;border-radius:4px;-webkit-box-shadow:inset 0 1px 1px rgba(0,0,0,0.075);box-shadow:inset 0 1px 1px rgba(0,0,0,0.075);-webkit-transition:border-color ease-in-out .15s, -webkit-box-shadow ease-in-out .15s;-o-transition:border-color ease-in-out .15s, box-shadow ease-in-out .15s;transition:border-color ease-in-out .15s, box-shadow ease-in-out .15s}.form-control:focus{border-color:#66afe9;outline:0;-webkit-box-shadow:inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 8px rgba(102, 175, 233, 0.6);box-shadow:inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 8px rgba(102, 175, 233, 0.6)}.form-control::-moz-placeholder{color:#999;opacity:1}.form-control:-ms-input-placeholder{color:#999}.form-control::-webkit-input-placeholder{color:#999}.form-control::-ms-expand{background-color:transparent;border:0}.form-control[disabled],.form-control[readonly],fieldset[disabled] .form-control{background-color:#eee;opacity:1}.form-control[disabled],fieldset[disabled] .form-control{cursor:not-allowed}textarea.form-control{height:auto}@media screen and (-webkit-min-device-pixel-ratio:0){input[type="date"].form-control,input[type="time"].form-control,input[type="datetime-local"].form-control,input[type="month"].form-control{line-height:34px}input[type="date"].input-sm,input[type="time"].input-sm,input[type="datetime-local"].input-sm,input[type="month"].input-sm,.input-group-sm input[type="date"],.input-group-sm input[type="time"],.input-group-sm input[type="datetime-local"],.input-group-sm input[type="month"]{line-height:30px}input[type="date"].input-lg,input[type="time"].input-lg,input[type="datetime-local"].input-lg,input[type="month"].input-lg,.input-group-lg input[type="date"],.input-group-lg input[type="time"],.input-group-lg input[type="datetime-local"],.input-group-lg input[type="month"]{line-height:46px}}.form-group{margin-bottom:15px}.radio,.checkbox{position:relative;display:block;margin-top:10px;margin-bottom:10px}.radio.disabled label,.checkbox.disabled label,fieldset[disabled] .radio label,fieldset[disabled] .checkbox label{cursor:not-allowed}.radio label,.checkbox label{min-height:20px;padding-left:20px;margin-bottom:0;font-weight:400;cursor:pointer}.radio input[type="radio"],.radio-inline input[type="radio"],.checkbox input[type="checkbox"],.checkbox-inline input[type="checkbox"]{position:absolute;margin-top:4px \9;margin-left:-20px}.radio+.radio,.checkbox+.checkbox{margin-top:-5px}.radio-inline,.checkbox-inline{position:relative;display:inline-block;padding-left:20px;margin-bottom:0;font-weight:400;vertical-align:middle;cursor:pointer}.radio-inline.disabled,.checkbox-inline.disabled,fieldset[disabled] .radio-inline,fieldset[disabled] .checkbox-inline{cursor:not-allowed}.radio-inline+.radio-inline,.checkbox-inline+.checkbox-inline{margin-top:0;margin-left:10px}.form-control-static{min-height:34px;padding-top:7px;padding-bottom:7px;margin-bottom:0}.form-control-static.input-lg,.form-control-static.input-sm{padding-right:0;padding-left:0}.input-sm{height:30px;padding:5px 10px;font-size:12px;line-height:1.5;border-radius:3px}select.input-sm{height:30px;line-height:30px}textarea.input-sm,select[multiple].input-sm{height:auto}.form-group-sm .form-control{height:30px;padding:5px 10px;font-size:12px;line-height:1.5;border-radius:3px}.form-group-sm select.form-control{height:30px;line-height:30px}.form-group-sm textarea.form-control,.form-group-sm select[multiple].form-control{height:auto}.form-group-sm .form-control-static{height:30px;min-height:32px;padding:6px 10px;font-size:12px;line-height:1.5}.input-lg{height:46px;padding:10px 16px;font-size:18px;line-height:1.3333333;border-radius:6px}select.input-lg{height:46px;line-height:46px}textarea.input-lg,select[multiple].input-lg{height:auto}.form-group-lg .form-control{height:46px;padding:10px 16px;font-size:18px;line-height:1.3333333;border-radius:6px}.form-group-lg select.form-control{height:46px;line-height:46px}.form-group-lg textarea.form-control,.form-group-lg select[multiple].form-control{height:auto}.form-group-lg .form-control-static{height:46px;min-height:38px;padding:11px 16px;font-size:18px;line-height:1.3333333}.has-feedback{position:relative}.has-feedback .form-control{padding-right:42.5px}.form-control-feedback{position:absolute;top:0;right:0;z-index:2;display:block;width:34px;height:34px;line-height:34px;text-align:center;pointer-events:none}.input-lg+.form-control-feedback,.input-group-lg+.form-control-feedback,.form-group-lg .form-control+.form-control-feedback{width:46px;height:46px;line-height:46px}.input-sm+.form-control-feedback,.input-group-sm+.form-control-feedback,.form-group-sm .form-control+.form-control-feedback{width:30px;height:30px;line-height:30px}.has-success .help-block,.has-success .control-label,.has-success .radio,.has-success .checkbox,.has-success .radio-inline,.has-success .checkbox-inline,.has-success.radio label,.has-success.checkbox label,.has-success.radio-inline label,.has-success.checkbox-inline label{color:#3c763d}.has-success .form-control{border-color:#3c763d;-webkit-box-shadow:inset 0 1px 1px rgba(0,0,0,0.075);box-shadow:inset 0 1px 1px rgba(0,0,0,0.075)}.has-success .form-control:focus{border-color:#2b542c;-webkit-box-shadow:inset 0 1px 1px rgba(0,0,0,0.075),0 0 6px #67b168;box-shadow:inset 0 1px 1px rgba(0,0,0,0.075),0 0 6px #67b168}.has-success .input-group-addon{color:#3c763d;background-color:#dff0d8;border-color:#3c763d}.has-success .form-control-feedback{color:#3c763d}.has-warning .help-block,.has-warning .control-label,.has-warning .radio,.has-warning .checkbox,.has-warning .radio-inline,.has-warning .checkbox-inline,.has-warning.radio label,.has-warning.checkbox label,.has-warning.radio-inline label,.has-warning.checkbox-inline label{color:#8a6d3b}.has-warning .form-control{border-color:#8a6d3b;-webkit-box-shadow:inset 0 1px 1px rgba(0,0,0,0.075);box-shadow:inset 0 1px 1px rgba(0,0,0,0.075)}.has-warning .form-control:focus{border-color:#66512c;-webkit-box-shadow:inset 0 1px 1px rgba(0,0,0,0.075),0 0 6px #c0a16b;box-shadow:inset 0 1px 1px rgba(0,0,0,0.075),0 0 6px #c0a16b}.has-warning .input-group-addon{color:#8a6d3b;background-color:#fcf8e3;border-color:#8a6d3b}.has-warning .form-control-feedback{color:#8a6d3b}.has-error .help-block,.has-error .control-label,.has-error .radio,.has-error .checkbox,.has-error .radio-inline,.has-error .checkbox-inline,.has-error.radio label,.has-error.checkbox label,.has-error.radio-inline label,.has-error.checkbox-inline label{color:#a94442}.has-error .form-control{border-color:#a94442;-webkit-box-shadow:inset 0 1px 1px rgba(0,0,0,0.075);box-shadow:inset 0 1px 1px rgba(0,0,0,0.075)}.has-error .form-control:focus{border-color:#843534;-webkit-box-shadow:inset 0 1px 1px rgba(0,0,0,0.075),0 0 6px #ce8483;box-shadow:inset 0 1px 1px rgba(0,0,0,0.075),0 0 6px #ce8483}.has-error .input-group-addon{color:#a94442;background-color:#f2dede;border-color:#a94442}.has-error .form-control-feedback{color:#a94442}.has-feedback label~.form-control-feedback{top:25px}.has-feedback label.sr-only~.form-control-feedback{top:0}.help-block{display:block;margin-top:5px;margin-bottom:10px;color:#737373}@media (min-width:768px){.form-inline .form-group{display:inline-block;margin-bottom:0;vertical-align:middle}.form-inline .form-control{display:inline-block;width:auto;vertical-align:middle}.form-inline .form-control-static{display:inline-block}.form-inline .input-group{display:inline-table;vertical-align:middle}.form-inline .input-group .input-group-addon,.form-inline .input-group .input-group-btn,.form-inline .input-group .form-control{width:auto}.form-inline .input-group>.form-control{width:100%}.form-inline .control-label{margin-bottom:0;vertical-align:middle}.form-inline .radio,.form-inline .checkbox{display:inline-block;margin-top:0;margin-bottom:0;vertical-align:middle}.form-inline .radio label,.form-inline .checkbox label{padding-left:0}.form-inline .radio input[type="radio"],.form-inline .checkbox input[type="checkbox"]{position:relative;margin-left:0}.form-inline .has-feedback .form-control-feedback{top:0}}.form-horizontal .radio,.form-horizontal .checkbox,.form-horizontal .radio-inline,.form-horizontal .checkbox-inline{padding-top:7px;margin-top:0;margin-bottom:0}.form-horizontal .radio,.form-horizontal .checkbox{min-height:27px}.form-horizontal .form-group{margin-right:-15px;margin-left:-15px}@media (min-width:768px){.form-horizontal .control-label{padding-top:7px;margin-bottom:0;text-align:right}}.form-horizontal .has-feedback .form-control-feedback{right:15px}@media (min-width:768px){.form-horizontal .form-group-lg .control-label{padding-top:11px;font-size:18px}}@media (min-width:768px){.form-horizontal .form-group-sm .control-label{padding-top:6px;font-size:12px}}.btn{display:inline-block;margin-bottom:0;font-weight:normal;text-align:center;white-space:nowrap;vertical-align:middle;-ms-touch-action:manipulation;touch-action:manipulation;cursor:pointer;background-image:none;border:1px solid transparent;padding:6px 12px;font-size:14px;line-height:1.42857143;border-radius:4px;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.btn:focus,.btn:active:focus,.btn.active:focus,.btn.focus,.btn:active.focus,.btn.active.focus{outline:5px auto -webkit-focus-ring-color;outline-offset:-2px}.btn:hover,.btn:focus,.btn.focus{color:#333;text-decoration:none}.btn:active,.btn.active{background-image:none;outline:0;-webkit-box-shadow:inset 0 3px 5px rgba(0,0,0,0.125);box-shadow:inset 0 3px 5px rgba(0,0,0,0.125)}.btn.disabled,.btn[disabled],fieldset[disabled] .btn{cursor:not-allowed;filter:alpha(opacity=65);opacity:.65;-webkit-box-shadow:none;box-shadow:none}a.btn.disabled,fieldset[disabled] a.btn{pointer-events:none}.btn-default{color:#333;background-color:#fff;border-color:#ccc}.btn-default:focus,.btn-default.focus{color:#333;background-color:#e6e6e6;border-color:#8c8c8c}.btn-default:hover{color:#333;background-color:#e6e6e6;border-color:#adadad}.btn-default:active,.btn-default.active,.open>.dropdown-toggle.btn-default{color:#333;background-color:#e6e6e6;background-image:none;border-color:#adadad}.btn-default:active:hover,.btn-default.active:hover,.open>.dropdown-toggle.btn-default:hover,.btn-default:active:focus,.btn-default.active:focus,.open>.dropdown-toggle.btn-default:focus,.btn-default:active.focus,.btn-default.active.focus,.open>.dropdown-toggle.btn-default.focus{color:#333;background-color:#d4d4d4;border-color:#8c8c8c}.btn-default.disabled:hover,.btn-default[disabled]:hover,fieldset[disabled] .btn-default:hover,.btn-default.disabled:focus,.btn-default[disabled]:focus,fieldset[disabled] .btn-default:focus,.btn-default.disabled.focus,.btn-default[disabled].focus,fieldset[disabled] .btn-default.focus{background-color:#fff;border-color:#ccc}.btn-default .badge{color:#fff;background-color:#333}.btn-primary{color:#fff;background-color:#337ab7;border-color:#2e6da4}.btn-primary:focus,.btn-primary.focus{color:#fff;background-color:#286090;border-color:#122b40}.btn-primary:hover{color:#fff;background-color:#286090;border-color:#204d74}.btn-primary:active,.btn-primary.active,.open>.dropdown-toggle.btn-primary{color:#fff;background-color:#286090;background-image:none;border-color:#204d74}.btn-primary:active:hover,.btn-primary.active:hover,.open>.dropdown-toggle.btn-primary:hover,.btn-primary:active:focus,.btn-primary.active:focus,.open>.dropdown-toggle.btn-primary:focus,.btn-primary:active.focus,.btn-primary.active.focus,.open>.dropdown-toggle.btn-primary.focus{color:#fff;background-color:#204d74;border-color:#122b40}.btn-primary.disabled:hover,.btn-primary[disabled]:hover,fieldset[disabled] .btn-primary:hover,.btn-primary.disabled:focus,.btn-primary[disabled]:focus,fieldset[disabled] .btn-primary:focus,.btn-primary.disabled.focus,.btn-primary[disabled].focus,fieldset[disabled] .btn-primary.focus{background-color:#337ab7;border-color:#2e6da4}.btn-primary .badge{color:#337ab7;background-color:#fff}.btn-success{color:#fff;background-color:#5cb85c;border-color:#4cae4c}.btn-success:focus,.btn-success.focus{color:#fff;background-color:#449d44;border-color:#255625}.btn-success:hover{color:#fff;background-color:#449d44;border-color:#398439}.btn-success:active,.btn-success.active,.open>.dropdown-toggle.btn-success{color:#fff;background-color:#449d44;background-image:none;border-color:#398439}.btn-success:active:hover,.btn-success.active:hover,.open>.dropdown-toggle.btn-success:hover,.btn-success:active:focus,.btn-success.active:focus,.open>.dropdown-toggle.btn-success:focus,.btn-success:active.focus,.btn-success.active.focus,.open>.dropdown-toggle.btn-success.focus{color:#fff;background-color:#398439;border-color:#255625}.btn-success.disabled:hover,.btn-success[disabled]:hover,fieldset[disabled] .btn-success:hover,.btn-success.disabled:focus,.btn-success[disabled]:focus,fieldset[disabled] .btn-success:focus,.btn-success.disabled.focus,.btn-success[disabled].focus,fieldset[disabled] .btn-success.focus{background-color:#5cb85c;border-color:#4cae4c}.btn-success .badge{color:#5cb85c;background-color:#fff}.btn-info{color:#fff;background-color:#5bc0de;border-color:#46b8da}.btn-info:focus,.btn-info.focus{color:#fff;background-color:#31b0d5;border-color:#1b6d85}.btn-info:hover{color:#fff;background-color:#31b0d5;border-color:#269abc}.btn-info:active,.btn-info.active,.open>.dropdown-toggle.btn-info{color:#fff;background-color:#31b0d5;background-image:none;border-color:#269abc}.btn-info:active:hover,.btn-info.active:hover,.open>.dropdown-toggle.btn-info:hover,.btn-info:active:focus,.btn-info.active:focus,.open>.dropdown-toggle.btn-info:focus,.btn-info:active.focus,.btn-info.active.focus,.open>.dropdown-toggle.btn-info.focus{color:#fff;background-color:#269abc;border-color:#1b6d85}.btn-info.disabled:hover,.btn-info[disabled]:hover,fieldset[disabled] .btn-info:hover,.btn-info.disabled:focus,.btn-info[disabled]:focus,fieldset[disabled] .btn-info:focus,.btn-info.disabled.focus,.btn-info[disabled].focus,fieldset[disabled] .btn-info.focus{background-color:#5bc0de;border-color:#46b8da}.btn-info .badge{color:#5bc0de;background-color:#fff}.btn-warning{color:#fff;background-color:#f0ad4e;border-color:#eea236}.btn-warning:focus,.btn-warning.focus{color:#fff;background-color:#ec971f;border-color:#985f0d}.btn-warning:hover{color:#fff;background-color:#ec971f;border-color:#d58512}.btn-warning:active,.btn-warning.active,.open>.dropdown-toggle.btn-warning{color:#fff;background-color:#ec971f;background-image:none;border-color:#d58512}.btn-warning:active:hover,.btn-warning.active:hover,.open>.dropdown-toggle.btn-warning:hover,.btn-warning:active:focus,.btn-warning.active:focus,.open>.dropdown-toggle.btn-warning:focus,.btn-warning:active.focus,.btn-warning.active.focus,.open>.dropdown-toggle.btn-warning.focus{color:#fff;background-color:#d58512;border-color:#985f0d}.btn-warning.disabled:hover,.btn-warning[disabled]:hover,fieldset[disabled] .btn-warning:hover,.btn-warning.disabled:focus,.btn-warning[disabled]:focus,fieldset[disabled] .btn-warning:focus,.btn-warning.disabled.focus,.btn-warning[disabled].focus,fieldset[disabled] .btn-warning.focus{background-color:#f0ad4e;border-color:#eea236}.btn-warning .badge{color:#f0ad4e;background-color:#fff}.btn-danger{color:#fff;background-color:#d9534f;border-color:#d43f3a}.btn-danger:focus,.btn-danger.focus{color:#fff;background-color:#c9302c;border-color:#761c19}.btn-danger:hover{color:#fff;background-color:#c9302c;border-color:#ac2925}.btn-danger:active,.btn-danger.active,.open>.dropdown-toggle.btn-danger{color:#fff;background-color:#c9302c;background-image:none;border-color:#ac2925}.btn-danger:active:hover,.btn-danger.active:hover,.open>.dropdown-toggle.btn-danger:hover,.btn-danger:active:focus,.btn-danger.active:focus,.open>.dropdown-toggle.btn-danger:focus,.btn-danger:active.focus,.btn-danger.active.focus,.open>.dropdown-toggle.btn-danger.focus{color:#fff;background-color:#ac2925;border-color:#761c19}.btn-danger.disabled:hover,.btn-danger[disabled]:hover,fieldset[disabled] .btn-danger:hover,.btn-danger.disabled:focus,.btn-danger[disabled]:focus,fieldset[disabled] .btn-danger:focus,.btn-danger.disabled.focus,.btn-danger[disabled].focus,fieldset[disabled] .btn-danger.focus{background-color:#d9534f;border-color:#d43f3a}.btn-danger .badge{color:#d9534f;background-color:#fff}.btn-link{font-weight:400;color:#337ab7;border-radius:0}.btn-link,.btn-link:active,.btn-link.active,.btn-link[disabled],fieldset[disabled] .btn-link{background-color:transparent;-webkit-box-shadow:none;box-shadow:none}.btn-link,.btn-link:hover,.btn-link:focus,.btn-link:active{border-color:transparent}.btn-link:hover,.btn-link:focus{color:#23527c;text-decoration:underline;background-color:transparent}.btn-link[disabled]:hover,fieldset[disabled] .btn-link:hover,.btn-link[disabled]:focus,fieldset[disabled] .btn-link:focus{color:#777;text-decoration:none}.btn-lg,.btn-group-lg>.btn{padding:10px 16px;font-size:18px;line-height:1.3333333;border-radius:6px}.btn-sm,.btn-group-sm>.btn{padding:5px 10px;font-size:12px;line-height:1.5;border-radius:3px}.btn-xs,.btn-group-xs>.btn{padding:1px 5px;font-size:12px;line-height:1.5;border-radius:3px}.btn-block{display:block;width:100%}.btn-block+.btn-block{margin-top:5px}input[type="submit"].btn-block,input[type="reset"].btn-block,input[type="button"].btn-block{width:100%}.btn-group,.btn-group-vertical{position:relative;display:inline-block;vertical-align:middle}.btn-group>.btn,.btn-group-vertical>.btn{position:relative;float:left}.btn-group>.btn:hover,.btn-group-vertical>.btn:hover,.btn-group>.btn:focus,.btn-group-vertical>.btn:focus,.btn-group>.btn:active,.btn-group-vertical>.btn:active,.btn-group>.btn.active,.btn-group-vertical>.btn.active{z-index:2}.btn-group .btn+.btn,.btn-group .btn+.btn-group,.btn-group .btn-group+.btn,.btn-group .btn-group+.btn-group{margin-left:-1px}.btn-toolbar{margin-left:-5px}.btn-toolbar .btn,.btn-toolbar .btn-group,.btn-toolbar .input-group{float:left}.btn-toolbar>.btn,.btn-toolbar>.btn-group,.btn-toolbar>.input-group{margin-left:5px}.btn-group>.btn:not(:first-child):not(:last-child):not(.dropdown-toggle){border-radius:0}.btn-group>.btn:first-child{margin-left:0}.btn-group>.btn:first-child:not(:last-child):not(.dropdown-toggle){border-top-right-radius:0;border-bottom-right-radius:0}.btn-group>.btn:last-child:not(:first-child),.btn-group>.dropdown-toggle:not(:first-child){border-top-left-radius:0;border-bottom-left-radius:0}.btn-group>.btn-group{float:left}.btn-group>.btn-group:not(:first-child):not(:last-child)>.btn{border-radius:0}.btn-group>.btn-group:first-child:not(:last-child)>.btn:last-child,.btn-group>.btn-group:first-child:not(:last-child)>.dropdown-toggle{border-top-right-radius:0;border-bottom-right-radius:0}.btn-group>.btn-group:last-child:not(:first-child)>.btn:first-child{border-top-left-radius:0;border-bottom-left-radius:0}.btn-group .dropdown-toggle:active,.btn-group.open .dropdown-toggle{outline:0}.btn-group>.btn+.dropdown-toggle{padding-right:8px;padding-left:8px}.btn-group>.btn-lg+.dropdown-toggle{padding-right:12px;padding-left:12px}.btn-group.open .dropdown-toggle{-webkit-box-shadow:inset 0 3px 5px rgba(0,0,0,0.125);box-shadow:inset 0 3px 5px rgba(0,0,0,0.125)}.btn-group.open .dropdown-toggle.btn-link{-webkit-box-shadow:none;box-shadow:none}.btn .caret{margin-left:0}.btn-lg .caret{border-width:5px 5px 0;border-bottom-width:0}.dropup .btn-lg .caret{border-width:0 5px 5px}.btn-group-vertical>.btn,.btn-group-vertical>.btn-group,.btn-group-vertical>.btn-group>.btn{display:block;float:none;width:100%;max-width:100%}.btn-group-vertical>.btn-group>.btn{float:none}.btn-group-vertical>.btn+.btn,.btn-group-vertical>.btn+.btn-group,.btn-group-vertical>.btn-group+.btn,.btn-group-vertical>.btn-group+.btn-group{margin-top:-1px;margin-left:0}.btn-group-vertical>.btn:not(:first-child):not(:last-child){border-radius:0}.btn-group-vertical>.btn:first-child:not(:last-child){border-top-left-radius:4px;border-top-right-radius:4px;border-bottom-right-radius:0;border-bottom-left-radius:0}.btn-group-vertical>.btn:last-child:not(:first-child){border-top-left-radius:0;border-top-right-radius:0;border-bottom-right-radius:4px;border-bottom-left-radius:4px}.btn-group-vertical>.btn-group:not(:first-child):not(:last-child)>.btn{border-radius:0}.btn-group-vertical>.btn-group:first-child:not(:last-child)>.btn:last-child,.btn-group-vertical>.btn-group:first-child:not(:last-child)>.dropdown-toggle{border-bottom-right-radius:0;border-bottom-left-radius:0}.btn-group-vertical>.btn-group:last-child:not(:first-child)>.btn:first-child{border-top-left-radius:0;border-top-right-radius:0}.btn-group-justified{display:table;width:100%;table-layout:fixed;border-collapse:separate}.btn-group-justified>.btn,.btn-group-justified>.btn-group{display:table-cell;float:none;width:1%}.btn-group-justified>.btn-group .btn{width:100%}.btn-group-justified>.btn-group .dropdown-menu{left:auto}[data-toggle="buttons"]>.btn input[type="radio"],[data-toggle="buttons"]>.btn-group>.btn input[type="radio"],[data-toggle="buttons"]>.btn input[type="checkbox"],[data-toggle="buttons"]>.btn-group>.btn input[type="checkbox"]{position:absolute;clip:rect(0, 0, 0, 0);pointer-events:none}.input-group{position:relative;display:table;border-collapse:separate}.input-group[class*="col-"]{float:none;padding-right:0;padding-left:0}.input-group .form-control{position:relative;z-index:2;float:left;width:100%;margin-bottom:0}.input-group .form-control:focus{z-index:3}.input-group-lg>.form-control,.input-group-lg>.input-group-addon,.input-group-lg>.input-group-btn>.btn{height:46px;padding:10px 16px;font-size:18px;line-height:1.3333333;border-radius:6px}select.input-group-lg>.form-control,select.input-group-lg>.input-group-addon,select.input-group-lg>.input-group-btn>.btn{height:46px;line-height:46px}textarea.input-group-lg>.form-control,textarea.input-group-lg>.input-group-addon,textarea.input-group-lg>.input-group-btn>.btn,select[multiple].input-group-lg>.form-control,select[multiple].input-group-lg>.input-group-addon,select[multiple].input-group-lg>.input-group-btn>.btn{height:auto}.input-group-sm>.form-control,.input-group-sm>.input-group-addon,.input-group-sm>.input-group-btn>.btn{height:30px;padding:5px 10px;font-size:12px;line-height:1.5;border-radius:3px}select.input-group-sm>.form-control,select.input-group-sm>.input-group-addon,select.input-group-sm>.input-group-btn>.btn{height:30px;line-height:30px}textarea.input-group-sm>.form-control,textarea.input-group-sm>.input-group-addon,textarea.input-group-sm>.input-group-btn>.btn,select[multiple].input-group-sm>.form-control,select[multiple].input-group-sm>.input-group-addon,select[multiple].input-group-sm>.input-group-btn>.btn{height:auto}.input-group-addon,.input-group-btn,.input-group .form-control{display:table-cell}.input-group-addon:not(:first-child):not(:last-child),.input-group-btn:not(:first-child):not(:last-child),.input-group .form-control:not(:first-child):not(:last-child){border-radius:0}.input-group-addon,.input-group-btn{width:1%;white-space:nowrap;vertical-align:middle}.input-group-addon{padding:6px 12px;font-size:14px;font-weight:400;line-height:1;color:#555;text-align:center;background-color:#eee;border:1px solid #ccc;border-radius:4px}.input-group-addon.input-sm{padding:5px 10px;font-size:12px;border-radius:3px}.input-group-addon.input-lg{padding:10px 16px;font-size:18px;border-radius:6px}.input-group-addon input[type="radio"],.input-group-addon input[type="checkbox"]{margin-top:0}.input-group .form-control:first-child,.input-group-addon:first-child,.input-group-btn:first-child>.btn,.input-group-btn:first-child>.btn-group>.btn,.input-group-btn:first-child>.dropdown-toggle,.input-group-btn:last-child>.btn:not(:last-child):not(.dropdown-toggle),.input-group-btn:last-child>.btn-group:not(:last-child)>.btn{border-top-right-radius:0;border-bottom-right-radius:0}.input-group-addon:first-child{border-right:0}.input-group .form-control:last-child,.input-group-addon:last-child,.input-group-btn:last-child>.btn,.input-group-btn:last-child>.btn-group>.btn,.input-group-btn:last-child>.dropdown-toggle,.input-group-btn:first-child>.btn:not(:first-child),.input-group-btn:first-child>.btn-group:not(:first-child)>.btn{border-top-left-radius:0;border-bottom-left-radius:0}.input-group-addon:last-child{border-left:0}.input-group-btn{position:relative;font-size:0;white-space:nowrap}.input-group-btn>.btn{position:relative}.input-group-btn>.btn+.btn{margin-left:-1px}.input-group-btn>.btn:hover,.input-group-btn>.btn:focus,.input-group-btn>.btn:active{z-index:2}.input-group-btn:first-child>.btn,.input-group-btn:first-child>.btn-group{margin-right:-1px}.input-group-btn:last-child>.btn,.input-group-btn:last-child>.btn-group{z-index:2;margin-left:-1px}.nav{padding-left:0;margin-bottom:0;list-style:none}.nav>li{position:relative;display:block}.nav>li>a{position:relative;display:block;padding:10px 15px}.nav>li>a:hover,.nav>li>a:focus{text-decoration:none;background-color:#eee}.nav>li.disabled>a{color:#777}.nav>li.disabled>a:hover,.nav>li.disabled>a:focus{color:#777;text-decoration:none;cursor:not-allowed;background-color:transparent}.nav .open>a,.nav .open>a:hover,.nav .open>a:focus{background-color:#eee;border-color:#337ab7}.nav .nav-divider{height:1px;margin:9px 0;overflow:hidden;background-color:#e5e5e5}.nav>li>a>img{max-width:none}.nav-tabs{border-bottom:1px solid #ddd}.nav-tabs>li{float:left;margin-bottom:-1px}.nav-tabs>li>a{margin-right:2px;line-height:1.42857143;border:1px solid transparent;border-radius:4px 4px 0 0}.nav-tabs>li>a:hover{border-color:#eee #eee #ddd}.nav-tabs>li.active>a,.nav-tabs>li.active>a:hover,.nav-tabs>li.active>a:focus{color:#555;cursor:default;background-color:#fff;border:1px solid #ddd;border-bottom-color:transparent}.nav-tabs.nav-justified{width:100%;border-bottom:0}.nav-tabs.nav-justified>li{float:none}.nav-tabs.nav-justified>li>a{margin-bottom:5px;text-align:center}.nav-tabs.nav-justified>.dropdown .dropdown-menu{top:auto;left:auto}@media (min-width:768px){.nav-tabs.nav-justified>li{display:table-cell;width:1%}.nav-tabs.nav-justified>li>a{margin-bottom:0}}.nav-tabs.nav-justified>li>a{margin-right:0;border-radius:4px}.nav-tabs.nav-justified>.active>a,.nav-tabs.nav-justified>.active>a:hover,.nav-tabs.nav-justified>.active>a:focus{border:1px solid #ddd}@media (min-width:768px){.nav-tabs.nav-justified>li>a{border-bottom:1px solid #ddd;border-radius:4px 4px 0 0}.nav-tabs.nav-justified>.active>a,.nav-tabs.nav-justified>.active>a:hover,.nav-tabs.nav-justified>.active>a:focus{border-bottom-color:#fff}}.nav-pills>li{float:left}.nav-pills>li>a{border-radius:4px}.nav-pills>li+li{margin-left:2px}.nav-pills>li.active>a,.nav-pills>li.active>a:hover,.nav-pills>li.active>a:focus{color:#fff;background-color:#337ab7}.nav-stacked>li{float:none}.nav-stacked>li+li{margin-top:2px;margin-left:0}.nav-justified{width:100%}.nav-justified>li{float:none}.nav-justified>li>a{margin-bottom:5px;text-align:center}.nav-justified>.dropdown .dropdown-menu{top:auto;left:auto}@media (min-width:768px){.nav-justified>li{display:table-cell;width:1%}.nav-justified>li>a{margin-bottom:0}}.nav-tabs-justified{border-bottom:0}.nav-tabs-justified>li>a{margin-right:0;border-radius:4px}.nav-tabs-justified>.active>a,.nav-tabs-justified>.active>a:hover,.nav-tabs-justified>.active>a:focus{border:1px solid #ddd}@media (min-width:768px){.nav-tabs-justified>li>a{border-bottom:1px solid #ddd;border-radius:4px 4px 0 0}.nav-tabs-justified>.active>a,.nav-tabs-justified>.active>a:hover,.nav-tabs-justified>.active>a:focus{border-bottom-color:#fff}}.tab-content>.tab-pane{display:none}.tab-content>.active{display:block}.nav-tabs .dropdown-menu{margin-top:-1px;border-top-left-radius:0;border-top-right-radius:0}.label{display:inline;padding:.2em .6em .3em;font-size:75%;font-weight:700;line-height:1;color:#fff;text-align:center;white-space:nowrap;vertical-align:baseline;border-radius:.25em}a.label:hover,a.label:focus{color:#fff;text-decoration:none;cursor:pointer}.label:empty{display:none}.btn .label{position:relative;top:-1px}.label-default{background-color:#777}.label-default[href]:hover,.label-default[href]:focus{background-color:#5e5e5e}.label-primary{background-color:#337ab7}.label-primary[href]:hover,.label-primary[href]:focus{background-color:#286090}.label-success{background-color:#5cb85c}.label-success[href]:hover,.label-success[href]:focus{background-color:#449d44}.label-info{background-color:#5bc0de}.label-info[href]:hover,.label-info[href]:focus{background-color:#31b0d5}.label-warning{background-color:#f0ad4e}.label-warning[href]:hover,.label-warning[href]:focus{background-color:#ec971f}.label-danger{background-color:#d9534f}.label-danger[href]:hover,.label-danger[href]:focus{background-color:#c9302c}.badge{display:inline-block;min-width:10px;padding:3px 7px;font-size:12px;font-weight:bold;line-height:1;color:#fff;text-align:center;white-space:nowrap;vertical-align:middle;background-color:#777;border-radius:10px}.badge:empty{display:none}.btn .badge{position:relative;top:-1px}.btn-xs .badge,.btn-group-xs>.btn .badge{top:0;padding:1px 5px}a.badge:hover,a.badge:focus{color:#fff;text-decoration:none;cursor:pointer}.list-group-item.active>.badge,.nav-pills>.active>a>.badge{color:#337ab7;background-color:#fff}.list-group-item>.badge{float:right}.list-group-item>.badge+.badge{margin-right:5px}.nav-pills>li>a>.badge{margin-left:3px}.panel{margin-bottom:20px;background-color:#fff;border:1px solid transparent;border-radius:4px;-webkit-box-shadow:0 1px 1px rgba(0,0,0,0.05);box-shadow:0 1px 1px rgba(0,0,0,0.05)}.panel-body{padding:15px}.panel-heading{padding:10px 15px;border-bottom:1px solid transparent;border-top-left-radius:3px;border-top-right-radius:3px}.panel-heading>.dropdown .dropdown-toggle{color:inherit}.panel-title{margin-top:0;margin-bottom:0;font-size:16px;color:inherit}.panel-title>a,.panel-title>small,.panel-title>.small,.panel-title>small>a,.panel-title>.small>a{color:inherit}.panel-footer{padding:10px 15px;background-color:#f5f5f5;border-top:1px solid #ddd;border-bottom-right-radius:3px;border-bottom-left-radius:3px}.panel>.list-group,.panel>.panel-collapse>.list-group{margin-bottom:0}.panel>.list-group .list-group-item,.panel>.panel-collapse>.list-group .list-group-item{border-width:1px 0;border-radius:0}.panel>.list-group:first-child .list-group-item:first-child,.panel>.panel-collapse>.list-group:first-child .list-group-item:first-child{border-top:0;border-top-left-radius:3px;border-top-right-radius:3px}.panel>.list-group:last-child .list-group-item:last-child,.panel>.panel-collapse>.list-group:last-child .list-group-item:last-child{border-bottom:0;border-bottom-right-radius:3px;border-bottom-left-radius:3px}.panel>.panel-heading+.panel-collapse>.list-group .list-group-item:first-child{border-top-left-radius:0;border-top-right-radius:0}.panel-heading+.list-group .list-group-item:first-child{border-top-width:0}.list-group+.panel-footer{border-top-width:0}.panel>.table,.panel>.table-responsive>.table,.panel>.panel-collapse>.table{margin-bottom:0}.panel>.table caption,.panel>.table-responsive>.table caption,.panel>.panel-collapse>.table caption{padding-right:15px;padding-left:15px}.panel>.table:first-child,.panel>.table-responsive:first-child>.table:first-child{border-top-left-radius:3px;border-top-right-radius:3px}.panel>.table:first-child>thead:first-child>tr:first-child,.panel>.table-responsive:first-child>.table:first-child>thead:first-child>tr:first-child,.panel>.table:first-child>tbody:first-child>tr:first-child,.panel>.table-responsive:first-child>.table:first-child>tbody:first-child>tr:first-child{border-top-left-radius:3px;border-top-right-radius:3px}.panel>.table:first-child>thead:first-child>tr:first-child td:first-child,.panel>.table-responsive:first-child>.table:first-child>thead:first-child>tr:first-child td:first-child,.panel>.table:first-child>tbody:first-child>tr:first-child td:first-child,.panel>.table-responsive:first-child>.table:first-child>tbody:first-child>tr:first-child td:first-child,.panel>.table:first-child>thead:first-child>tr:first-child th:first-child,.panel>.table-responsive:first-child>.table:first-child>thead:first-child>tr:first-child th:first-child,.panel>.table:first-child>tbody:first-child>tr:first-child th:first-child,.panel>.table-responsive:first-child>.table:first-child>tbody:first-child>tr:first-child th:first-child{border-top-left-radius:3px}.panel>.table:first-child>thead:first-child>tr:first-child td:last-child,.panel>.table-responsive:first-child>.table:first-child>thead:first-child>tr:first-child td:last-child,.panel>.table:first-child>tbody:first-child>tr:first-child td:last-child,.panel>.table-responsive:first-child>.table:first-child>tbody:first-child>tr:first-child td:last-child,.panel>.table:first-child>thead:first-child>tr:first-child th:last-child,.panel>.table-responsive:first-child>.table:first-child>thead:first-child>tr:first-child th:last-child,.panel>.table:first-child>tbody:first-child>tr:first-child th:last-child,.panel>.table-responsive:first-child>.table:first-child>tbody:first-child>tr:first-child th:last-child{border-top-right-radius:3px}.panel>.table:last-child,.panel>.table-responsive:last-child>.table:last-child{border-bottom-right-radius:3px;border-bottom-left-radius:3px}.panel>.table:last-child>tbody:last-child>tr:last-child,.panel>.table-responsive:last-child>.table:last-child>tbody:last-child>tr:last-child,.panel>.table:last-child>tfoot:last-child>tr:last-child,.panel>.table-responsive:last-child>.table:last-child>tfoot:last-child>tr:last-child{border-bottom-right-radius:3px;border-bottom-left-radius:3px}.panel>.table:last-child>tbody:last-child>tr:last-child td:first-child,.panel>.table-responsive:last-child>.table:last-child>tbody:last-child>tr:last-child td:first-child,.panel>.table:last-child>tfoot:last-child>tr:last-child td:first-child,.panel>.table-responsive:last-child>.table:last-child>tfoot:last-child>tr:last-child td:first-child,.panel>.table:last-child>tbody:last-child>tr:last-child th:first-child,.panel>.table-responsive:last-child>.table:last-child>tbody:last-child>tr:last-child th:first-child,.panel>.table:last-child>tfoot:last-child>tr:last-child th:first-child,.panel>.table-responsive:last-child>.table:last-child>tfoot:last-child>tr:last-child th:first-child{border-bottom-left-radius:3px}.panel>.table:last-child>tbody:last-child>tr:last-child td:last-child,.panel>.table-responsive:last-child>.table:last-child>tbody:last-child>tr:last-child td:last-child,.panel>.table:last-child>tfoot:last-child>tr:last-child td:last-child,.panel>.table-responsive:last-child>.table:last-child>tfoot:last-child>tr:last-child td:last-child,.panel>.table:last-child>tbody:last-child>tr:last-child th:last-child,.panel>.table-responsive:last-child>.table:last-child>tbody:last-child>tr:last-child th:last-child,.panel>.table:last-child>tfoot:last-child>tr:last-child th:last-child,.panel>.table-responsive:last-child>.table:last-child>tfoot:last-child>tr:last-child th:last-child{border-bottom-right-radius:3px}.panel>.panel-body+.table,.panel>.panel-body+.table-responsive,.panel>.table+.panel-body,.panel>.table-responsive+.panel-body{border-top:1px solid #ddd}.panel>.table>tbody:first-child>tr:first-child th,.panel>.table>tbody:first-child>tr:first-child td{border-top:0}.panel>.table-bordered,.panel>.table-responsive>.table-bordered{border:0}.panel>.table-bordered>thead>tr>th:first-child,.panel>.table-responsive>.table-bordered>thead>tr>th:first-child,.panel>.table-bordered>tbody>tr>th:first-child,.panel>.table-responsive>.table-bordered>tbody>tr>th:first-child,.panel>.table-bordered>tfoot>tr>th:first-child,.panel>.table-responsive>.table-bordered>tfoot>tr>th:first-child,.panel>.table-bordered>thead>tr>td:first-child,.panel>.table-responsive>.table-bordered>thead>tr>td:first-child,.panel>.table-bordered>tbody>tr>td:first-child,.panel>.table-responsive>.table-bordered>tbody>tr>td:first-child,.panel>.table-bordered>tfoot>tr>td:first-child,.panel>.table-responsive>.table-bordered>tfoot>tr>td:first-child{border-left:0}.panel>.table-bordered>thead>tr>th:last-child,.panel>.table-responsive>.table-bordered>thead>tr>th:last-child,.panel>.table-bordered>tbody>tr>th:last-child,.panel>.table-responsive>.table-bordered>tbody>tr>th:last-child,.panel>.table-bordered>tfoot>tr>th:last-child,.panel>.table-responsive>.table-bordered>tfoot>tr>th:last-child,.panel>.table-bordered>thead>tr>td:last-child,.panel>.table-responsive>.table-bordered>thead>tr>td:last-child,.panel>.table-bordered>tbody>tr>td:last-child,.panel>.table-responsive>.table-bordered>tbody>tr>td:last-child,.panel>.table-bordered>tfoot>tr>td:last-child,.panel>.table-responsive>.table-bordered>tfoot>tr>td:last-child{border-right:0}.panel>.table-bordered>thead>tr:first-child>td,.panel>.table-responsive>.table-bordered>thead>tr:first-child>td,.panel>.table-bordered>tbody>tr:first-child>td,.panel>.table-responsive>.table-bordered>tbody>tr:first-child>td,.panel>.table-bordered>thead>tr:first-child>th,.panel>.table-responsive>.table-bordered>thead>tr:first-child>th,.panel>.table-bordered>tbody>tr:first-child>th,.panel>.table-responsive>.table-bordered>tbody>tr:first-child>th{border-bottom:0}.panel>.table-bordered>tbody>tr:last-child>td,.panel>.table-responsive>.table-bordered>tbody>tr:last-child>td,.panel>.table-bordered>tfoot>tr:last-child>td,.panel>.table-responsive>.table-bordered>tfoot>tr:last-child>td,.panel>.table-bordered>tbody>tr:last-child>th,.panel>.table-responsive>.table-bordered>tbody>tr:last-child>th,.panel>.table-bordered>tfoot>tr:last-child>th,.panel>.table-responsive>.table-bordered>tfoot>tr:last-child>th{border-bottom:0}.panel>.table-responsive{margin-bottom:0;border:0}.panel-group{margin-bottom:20px}.panel-group .panel{margin-bottom:0;border-radius:4px}.panel-group .panel+.panel{margin-top:5px}.panel-group .panel-heading{border-bottom:0}.panel-group .panel-heading+.panel-collapse>.panel-body,.panel-group .panel-heading+.panel-collapse>.list-group{border-top:1px solid #ddd}.panel-group .panel-footer{border-top:0}.panel-group .panel-footer+.panel-collapse .panel-body{border-bottom:1px solid #ddd}.panel-default{border-color:#ddd}.panel-default>.panel-heading{color:#333;background-color:#f5f5f5;border-color:#ddd}.panel-default>.panel-heading+.panel-collapse>.panel-body{border-top-color:#ddd}.panel-default>.panel-heading .badge{color:#f5f5f5;background-color:#333}.panel-default>.panel-footer+.panel-collapse>.panel-body{border-bottom-color:#ddd}.panel-primary{border-color:#337ab7}.panel-primary>.panel-heading{color:#fff;background-color:#337ab7;border-color:#337ab7}.panel-primary>.panel-heading+.panel-collapse>.panel-body{border-top-color:#337ab7}.panel-primary>.panel-heading .badge{color:#337ab7;background-color:#fff}.panel-primary>.panel-footer+.panel-collapse>.panel-body{border-bottom-color:#337ab7}.panel-success{border-color:#d6e9c6}.panel-success>.panel-heading{color:#3c763d;background-color:#dff0d8;border-color:#d6e9c6}.panel-success>.panel-heading+.panel-collapse>.panel-body{border-top-color:#d6e9c6}.panel-success>.panel-heading .badge{color:#dff0d8;background-color:#3c763d}.panel-success>.panel-footer+.panel-collapse>.panel-body{border-bottom-color:#d6e9c6}.panel-info{border-color:#bce8f1}.panel-info>.panel-heading{color:#31708f;background-color:#d9edf7;border-color:#bce8f1}.panel-info>.panel-heading+.panel-collapse>.panel-body{border-top-color:#bce8f1}.panel-info>.panel-heading .badge{color:#d9edf7;background-color:#31708f}.panel-info>.panel-footer+.panel-collapse>.panel-body{border-bottom-color:#bce8f1}.panel-warning{border-color:#faebcc}.panel-warning>.panel-heading{color:#8a6d3b;background-color:#fcf8e3;border-color:#faebcc}.panel-warning>.panel-heading+.panel-collapse>.panel-body{border-top-color:#faebcc}.panel-warning>.panel-heading .badge{color:#fcf8e3;background-color:#8a6d3b}.panel-warning>.panel-footer+.panel-collapse>.panel-body{border-bottom-color:#faebcc}.panel-danger{border-color:#ebccd1}.panel-danger>.panel-heading{color:#a94442;background-color:#f2dede;border-color:#ebccd1}.panel-danger>.panel-heading+.panel-collapse>.panel-body{border-top-color:#ebccd1}.panel-danger>.panel-heading .badge{color:#f2dede;background-color:#a94442}.panel-danger>.panel-footer+.panel-collapse>.panel-body{border-bottom-color:#ebccd1}.close{float:right;font-size:21px;font-weight:bold;line-height:1;color:#000;text-shadow:0 1px 0 #fff;filter:alpha(opacity=20);opacity:.2}.close:hover,.close:focus{color:#000;text-decoration:none;cursor:pointer;filter:alpha(opacity=50);opacity:.5}button.close{padding:0;cursor:pointer;background:transparent;border:0;-webkit-appearance:none;appearance:none}.modal-open{overflow:hidden}.modal{position:fixed;top:0;right:0;bottom:0;left:0;z-index:1050;display:none;overflow:hidden;-webkit-overflow-scrolling:touch;outline:0}.modal.fade .modal-dialog{-webkit-transform:translate(0, -25%);-ms-transform:translate(0, -25%);-o-transform:translate(0, -25%);transform:translate(0, -25%);-webkit-transition:-webkit-transform 0.3s ease-out;-o-transition:-o-transform 0.3s ease-out;transition:transform 0.3s ease-out}.modal.in .modal-dialog{-webkit-transform:translate(0, 0);-ms-transform:translate(0, 0);-o-transform:translate(0, 0);transform:translate(0, 0)}.modal-open .modal{overflow-x:hidden;overflow-y:auto}.modal-dialog{position:relative;width:auto;margin:10px}.modal-content{position:relative;background-color:#fff;-webkit-background-clip:padding-box;background-clip:padding-box;border:1px solid #999;border:1px solid rgba(0,0,0,0.2);border-radius:6px;-webkit-box-shadow:0 3px 9px rgba(0,0,0,0.5);box-shadow:0 3px 9px rgba(0,0,0,0.5);outline:0}.modal-backdrop{position:fixed;top:0;right:0;bottom:0;left:0;z-index:1040;background-color:#000}.modal-backdrop.fade{filter:alpha(opacity=0);opacity:0}.modal-backdrop.in{filter:alpha(opacity=50);opacity:.5}.modal-header{padding:15px;border-bottom:1px solid #e5e5e5}.modal-header .close{margin-top:-2px}.modal-title{margin:0;line-height:1.42857143}.modal-body{position:relative;padding:15px}.modal-footer{padding:15px;text-align:right;border-top:1px solid #e5e5e5}.modal-footer .btn+.btn{margin-bottom:0;margin-left:5px}.modal-footer .btn-group .btn+.btn{margin-left:-1px}.modal-footer .btn-block+.btn-block{margin-left:0}.modal-scrollbar-measure{position:absolute;top:-9999px;width:50px;height:50px;overflow:scroll}@media (min-width:768px){.modal-dialog{width:600px;margin:30px auto}.modal-content{-webkit-box-shadow:0 5px 15px rgba(0,0,0,0.5);box-shadow:0 5px 15px rgba(0,0,0,0.5)}.modal-sm{width:300px}}@media (min-width:992px){.modal-lg{width:900px}}.clearfix:before,.clearfix:after,.form-horizontal .form-group:before,.form-horizontal .form-group:after,.btn-toolbar:before,.btn-toolbar:after,.btn-group-vertical>.btn-group:before,.btn-group-vertical>.btn-group:after,.nav:before,.nav:after,.panel-body:before,.panel-body:after,.modal-header:before,.modal-header:after,.modal-footer:before,.modal-footer:after{display:table;content:" "}.clearfix:after,.form-horizontal .form-group:after,.btn-toolbar:after,.btn-group-vertical>.btn-group:after,.nav:after,.panel-body:after,.modal-header:after,.modal-footer:after{clear:both}.center-block{display:block;margin-right:auto;margin-left:auto}.pull-right{float:right !important}.pull-left{float:left !important}.hide{display:none !important}.show{display:block !important}.invisible{visibility:hidden}.text-hide{font:0/0 a;color:transparent;text-shadow:none;background-color:transparent;border:0}.hidden{display:none !important}.affix{position:fixed}
 .fa-youtube:before{content:"\f16a"}.fa-flag:before {content: "\f024";}.fa::before{font-size: 92%;}
 body{color:black;}
 </style> 
  <style> 
@font-face {
  font-family: 'FontAwesome';
  src: url('../fonts/fontawesome-webfont.eot?v=4.5.0');
  src: url('../fonts/fontawesome-webfont.eot?#iefix&v=4.5.0') format('embedded-opentype'), url('../fonts/fontawesome-webfont.woff2?v=4.5.0') format('woff2'), url('../fonts/fontawesome-webfont.woff?v=4.5.0') format('woff'), url('../fonts/fontawesome-webfont.ttf?v=4.5.0') format('truetype'), url('../fonts/fontawesome-webfont.svg?v=4.5.0#fontawesomeregular') format('svg');
  font-weight: normal;
  font-style: normal;
}
.fa {
  display: inline-block;
  font: normal normal normal 14px/1 FontAwesome;
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}  
.fa-phone:before{content:"\f095"}
.fa-key:before{content:"\f084"}.fa-eye:before{content:"\f06e"}
.fa-search::before { content: ""; }
.fa-envelope-o::before { content: ""; }
.fa-heart::before { content: ""; }
.fa-star::before { content: ""; }
.fa-star-o::before { content: ""; }
.fa-user::before { content: ""; }
.fa-check::before { content: ""; }
.fa-remove::before, .fa-close::before, .fa-times::before { content: ""; }
.fa-search-plus::before { content: ""; }
.fa-search-minus::before { content: ""; }
.fa-gear::before, .fa-cog::before { content: ""; }
.fa-home::before { content: ""; }
.fa-refresh::before { content: ""; }
.fa-volume-up::before { content: ""; }
.fa-photo::before, .fa-image::before, .fa-picture-o::before { content: ""; }
.fa-edit::before, .fa-pencil-square-o::before { content: ""; }
.fa-check-square-o::before { content: ""; }
.fa-plus-circle::before { content: ""; }
.fa-minus-circle::before { content: ""; }
.fa-times-circle::before { content: ""; }
.fa-check-circle::before { content: ""; }
.fa-info-circle::before { content: ""; }
.fa-times-circle-o::before { content: ""; }
.fa-check-circle-o::before { content: ""; }
.fa-ban::before { content: ""; }
.fa-arrow-left::before { content: ""; }
.fa-arrow-right::before { content: ""; }
.fa-arrow-up::before { content: ""; }
.fa-expand::before { content: ""; }
.fa-compress::before { content: ""; }
.fa-plus::before { content: ""; }
.fa-minus::before { content: ""; }
.fa-warning::before, .fa-exclamation-triangle::before { content: ""; }
.fa-comment::before { content: ""; }
.fa-gears::before, .fa-cogs::before { content: ""; }
.fa-comments::before { content: ""; }
.fa-commentsx::before { content: " "; }
.fa-commentsx::after { content: " "; } 
.fa-star-half::before { content: ""; }
.fa-heart-o::before { content: ""; }
.fa-sign-out::before { content: ""; }
.fa-group::before, .fa-users::before { content: ""; }
.fa-save::before, .fa-floppy-o::before { content: ""; }
.fa-comment-o::before { content: ""; }
.fa-comments-o::before { content: ""; }
.fa-user-md::before { content: ""; }
.fa-plus-square::before { content: ""; }
.fa-star-half-empty::before, .fa-star-half-full::before, .fa-star-half-o::before { content: ""; }
.fa-info::before { content: ""; }
.fa-microphone::before { content: ""; }
.fa-microphone-slash::before { content: ""; }
.fa-minus-square::before { content: ""; }
.fa-minus-square-o::before { content: ""; }
.fa-check-square::before { content: ""; }
.fa-plus-square-o::before { content: ""; }
.fa-institution::before, .fa-bank::before, .fa-university::before { content: ""; }
.fa-send::before, .fa-paper-plane::before { content: ""; }
.fa-send-o::before, .fa-paper-plane-o::before { content: ""; }
.fa-share-alt::before { content: ""; }
.fa-share-alt-square::before { content: ""; }
.fa-diamond::before { content: ""; }
.fa-user-secret::before { content: ""; }
.fa-heartbeat::before { content: ""; }
.fa-user-plus::before { content: ""; }
.fa-user-times::before { content: ""; }
.fa-commenting::before { content: ""; }
.fa-commenting-o::before { content: ""; } 
.fa-sign-in:before{content:"\f090"}
.fa-sign-out:before{content:"\f08b"}
</style>
    <style> 
    .nav>li>a{padding: 10px 2px;text-align: center;}
      .fr {
    float: right;
  }
  .fl {
    float: left;
  }
  .tbox {
    overflow: scroll;
    border-radius: 2px;
    border: 1px solid lightgrey;
    padding: 6px;
    max-height: 32px;
    min-height: 32px;
    height: 32px;
    font-weight: bold;
    overflow: hidden;
    resize: none;
  }
  .hid {
    display: none;
  }
  .noflow {
    overflow: hidden;
  }
  .hand {
    cursor: hand;
  }
 
  .break {
    overflow: auto;
    word-wrap: break-word; /* IE 5+ */
    overflow-x: hidden;
    overflow-wrap: break-word;
  }
  #cp .tab-content .tab-pane{
    min-width: 400px!important;
  }
  .u-ico {
    margin-top: 1px;  
    max-height: 18px;object-fit: scale-down;
  } 
  .emoi {
    max-width: 240px;
    max-height: 20px;
  }
  .unread {
    background-color: #ffc89d !important;
  }
  .object-fit {
    object-fit: contain;
    object-position: center right;
  } 
  table,
  th,
  td {
    border: 0;
  } 
  th,
  td {
    padding: 0px;
  }
  table {
    border-spacing: 0;
    border-collapse: collapse;
  }
  .mini {
    font-size: 13.68px !important;
  }
  .corner {
    border-radius: 5px;
  }
  .minix {
    font-size: 12.16px !important;
  } 
   .nosel,
  .u-ico,
  .u-pic,
  .tago {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    cursor: default;
  }
  .center {
    margin: 0 auto;
  }
  .dots {
    display: inline-block;
    white-space: nowrap;
    overflow: hidden !important;
    text-overflow: ellipsis;
    max-width: 100%;
  }
  
  .borderg {
    border: 1px solid #f4f4f4;
  }
  .border {
    border: 1px solid black;
  } 
  .fitimg {
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: 50% 50%;
  } 
  .bord {
    border-right: 4px solid dodgerblue !important;
  }
  
  html {
    height: 100vh;
    width: 100vw;
  }  
  .fa {
    text-align: left;
  }
  
  .modal-header {
    padding: 4px;border-bottom:0px;
  }
  
  .badge {
    padding: 0px 3px;
  }
    
  button.btn {
    padding: 5px 5px;
  }
   
  .d2 {
    padding-bottom: 2px;
  }
  .d2,#rooms,#users{background-color: #fafafa!important;}
  .popover-content {
    padding: 1px;
  } 
    </style>
    <style> 
      * {
      font-family: serif;
      font-weight: bold;
      text-shadow: none!important;
      font-size: 15.2px !important;line-height: 1.3!important;  font-weight:700!important;text-shadow:none!important;
  } 

  .u-msg{line-height: 1.35!important;}
      .ae { 
        border: 1px solid black!important; 
        border-radius: 2px!important; 
        float: left;
        text-align: center;width: 19.4%!important; max-width: 86px;
        padding: 4px 0px;
        margin: 1px;margin-bottom: 2px;min-width: 76px;
      }
      .pmsgc {
        background-color: rgba(0, 77, 255, 0.08)!important; 
      }
      .ppmsgc {
        background-color: #f1f1ff!important;
      }
      .hmsg {
        background-color: linen!important;
      }
      .label-primary,
      .btn-primary,
      .bg-primary,
      .label-primary:hover,
      .btn-primary:hover,
      .btn-primary:focus {
        background-color: #438ac7;
        background-image: none;
      } 
      .bg {
        background-color: #525F6E;
      } 
      .bgg {
        background-color: lightslategray;
      } 
      .pophead {
        background-color: slategrey;
      }
      
      .light {
        background-color: #FAFAFA;
      }
      .label,.btn{
        border-radius: 1px;
      }
      .label-primary,.btn-primary{  
        background-color: #525F6E!important;
      }
      .hid {
        display: none;
      }
      #mic .mic,#muteall{
        background-color: #525F6E;
      }
      .xtyping {
    width: 4px;
    height: 4px;
    margin-right: 4px;
    background-color: #57585a;
    border-radius: 50%;
    animation-name: bounce;
    animation-duration: 1.3s;
    animation-timing-function: linear;
    animation-iteration-count: infinite;
  }
  .mic{
    width:53px;height:50px;margin:0px 1px;border-radius:4px;background-image: url(imgs/mic.png);background-size: cover;background-repeat: no-repeat;background-position: center;
  }
  .xtyping:first-of-type {
    margin: 0px 4px;
  }
  
  .xtyping:nth-of-type(2) {
    animation-delay: 0.15s;
  }
  
  .xtyping:nth-of-type(3) {
    animation-delay: 0.3s;
  }
  .flex-grow-1 {
    flex-grow: 1 !important;
  }
  .c-flex {
    display: flex;
      flex-direction: column;
  }
  .d-flex{
    display: flex; 
  }
  .flex-fill {
    flex: 1 1 auto !important;
  }
 
  .nav-pills li a{border-radius: 0px!important;}
      @keyframes bounce {
    0%,
    60%,
    100% {
      transform: translateY(0);
    }
    30% {
      transform: translateY(-4px);
    }
  } 
  td {
    border: 1px solid grey;
    font-weight: bold;
  }  
  th {
    background-color: cornflowerblue;
    color: white;
    padding: 5px;
  }
  .tab-pane{
    padding: 0px;
    width: 100%;
  }
   .pgr{
    -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
   }
  html {
      overflow: hidden;
  } 
  .tc{
    border-radius: 3px!important;margin: 2px 3px!important;
  } 
 td{overflow-y: hidden!important;} 
 
.uhtml{
  text-align:left;width:100%;margin:1px;margin-bottom: 0px; padding-right:1px;background-color: #fafafa;outline:1px solid lavender;
}
.uhtml .di1 .u-pic{
  min-width:54px;width:54px;min-height:48px;max-height:62px;background-size: auto;background-position: center; 
}
.uhtml .co.ico{
  width:20px;height:20px;
}
.uhtml .di1 span.muted{
  color:indianred;display: none;width: 16px;margin-right: -16px;
}
.uhtml .di1 img.ustat{
  width:4px;min-height:100%;max-height: 62px; 
}
.uhtml div.di1{
  padding-right: 16px;width: 100%;
}
.uhtml .di1 .di2{
  padding: 0px 3px;overflow: hidden;
}
.uhtml .di1 .di2 .d-flex{
  width:100%;display: flex;margin-top:1px;height: 21px;
}
.uhtml .di1 .di2 .u-msg{
  color:#888;padding:1px; text-align:  left;
}
.uhtml .di1 .di2 .u-topic{
  padding-top:1px; max-width:100%;border-radius: 2px;
}
.uhtml .di1 .di2 .u-ico{
  min-height: 14px;margin-top:1px;
}
.uhtml .di3{
  margin-left:-32px;
}
.uhtml .di3 .uhash{
  color:grey;font-size:70%!important;
}
#usearch::placeholder{
  color: white;
}
#rsearch::placeholder{
  color: white;
} 
.modal-backdrop.in{opacity: 0.65;}
    </style> 
</head> 
<body class="bg" onload="if (typeof load == 'undefined'){setTimeout('location.reload();',5000);return;};load();" style="background-color:#40404f;height:100%;max-height:100%;min-height:100%; margin: 0px; padding: 0px; overflow: hidden; ">
  <div style="width:100%; height:100%;max-height:100%;max-width:394px;padding:0px;" class="center-block bg dad">
   <div id="tlogins" class="border light fr break c-flex" style="z-index:1000;margin-left:-4px;height:100%;width:100%;max-width:390px;min-height:100%;">
      <div class="hid">
      شات الجوال
    </div>
      <h1 class="hid">
      شات الجوال.شات جوال,شات كتابي,شات للجوال,شات جوال الخليج, دردشة الجوال ، شات الجوال الكتابي ، شات جوال ، الجوال,شات دردشة جوال ، شات جوال للجوال ، شات الجوال الصوتي ، شات دردشة الجوال ، شات دردشة جوال ، شات الجوال الصوتي ، شات الجوال العربي ، شات الجوال الخليج شات دردشة الجوال
    </h1>
    <div class="hid">
      
    </div>
     <div style="width: 100%;display: block;">
      <a href="/" class="label bg d-flex fl" style="width:100%;padding:6px;border-radius:0px;text-align: left;">
      <div class="fl" style="margin-right: 2px;width: 32px;height: 32px;background-image: url('prv1.webp');background-position: center;background-repeat: no-repeat;background-size: auto;"></div><span class="flex-grow-1 dots">شات الجوال</span><span class="btn fr btn-success fa fa-refresh" style="margin:0px;margin-top:-2px;width: 39px;" ></span></a>
      <div id="bnr" style="width: 100%;">
      </div>
      <ul class="nav  nav-tabs fl" style="margin:0px;background-color:white;height:44px;width: 100%;"> 
        <li class="active"><a data-toggle="tab" style="padding: 10px 6px;" class="  fa fa-user" href="#l1">دخول الزوار</a></li>
        <li><a data-toggle="tab" style="padding: 10px 6px;" class=" fa fa-user" href="#l2">دخول الاعضاء</a></li>
        <li><a data-toggle="tab" style="padding: 10px 6px;" class=" fa fa-user-plus" href="#l3"> تسجيل عضويه</a></li>
      </ul>
      <div style="width:100%;height: 74px;" class="tab-content fl" >
        <div id="l1" style="padding:4px;width:100%;" class="tab-pane in active">
          <input style="width: 206px;margin-top:33px;padding:5px;border-radius: 2px;height:33px;" autocomplete="off" type="search" class="border corner" id="u1" placeholder="أكتب الاسم المستعار">
          <button style="margin-top:1px;" onclick="login(1);" aria-label="enter" class="btn btn-primary">دخول</button>
        </div>
        <div id="l2" style="padding:4px;width:100%;" class="tab-pane hid">
          <input style="width: 206px;padding:5px;border-radius: 2px;height:33px;" id="u2" class="border corner" placeholder="اكتب اسم العضو">
          <input style="width: 206px;padding:5px;border-radius: 2px;height:33px;" id="pass1" class="border corner" type="password" placeholder="اكتب كلمه المرور">
          <button onclick="login(2);" class="btn btn-primary">دخول</button>
          <span onclick="$(this).toggleClass('label-info');$('#stealth').prop('checked',$(this).hasClass('label-info'));" style="color: black;padding:4px;margin:2px;border-radius:4px;" class="btn fr fa fa-eye"></span>
          <input style="display: none;" id="stealth" type="checkbox" value="">   
        </div>
        <div id="l3" style="padding:4px;width:100%;" class="tab-pane hid">
          <input style="width: 206px;padding:5px;border-radius: 2px;height:33px;" id="u3" class="border corner" placeholder="اكتب اسم العضو">
          <input style="width: 206px;padding:5px;border-radius: 2px;height:33px;" id="pass2" class="border corner" type="password" placeholder="اكتب كلمه المرور">
          <button onclick="login(3);" class="btn btn-primary">دخول</button>
        </div>
        <h6 class="hid">
          شات الجوال
        </h6> 
          <a class="hid" href="/">
          شات الجوال.شات جوال,شات كتابي,شات للجوال,شات جوال الخليج, دردشة الجوال ، شات الجوال الكتابي ، شات جوال ، الجوال,شات دردشة جوال ، شات جوال للجوال ، شات الجوال الصوتي ، شات دردشة الجوال ، شات دردشة جوال ، شات الجوال الصوتي ، شات الجوال العربي ، شات الجوال الخليج شات دردشة الجوال
        </a> 
      </div> 
    <div class="bg mini fl" style="width:100%;border-radius:0px;padding: 0px;height: 25px;">
      <label id="loginstat" class="fl label loginstat" style="border-radius: 1px;margin: 0px;height: 25px;min-width: 120px;">يتم الاتصال ..</label>
      <label id="s1" class="s1 fr fa fa-user label badgex label-success" style="border-radius: 0px;min-width: 52px;height: 25px;">0</label>
    </div>
  </div> 
      <div id="lonline" class="lonline light break flex-grow-1" style="width:100%;outline: lightgray solid 1px;padding-left:1px;background-color: #fafafa;">
      </div>
      <div class="fr borderg minix cop" style="padding:2px;background-color:white;z-index:1;color:#677889;font-size: small!important;height:22px;">
        Copyright © 2021 <a title="Jawalhost.com" class="mini" href="https://jawalhost.com/">جوال هوست</a>. All Rights Reserved
      </div>
    </div> 
  </div>
    <div id="room" style="height: 100%; width:100%;display: none;" class="break c-flex fr  ">
      <div id="mic" style="width:100%;border: none;padding: 2px; height: 54px;display: none;" class="nosel bg">
        <div id="muteall" onclick="muteAll();"
          style="margin-right:2px;width:48px;height:50px;color: black;background-color: mediumseagreen;"
          class="bg corner fl border ">
          <span style="margin-left: 10%;font-size: 38px!important;margin-top:-2px" class="fa fa-volume-up"></span>
        </div>
        <div id="mic0" i="0" class="fl mic dots border corner bg" style="background-size: cover;background-position: center;">
          <div class="u" style="display: flex;width: 100%;background: rgba(222, 222, 222, 0.35);margin-top: 26px;">
            <img class="object-fit" style="min-height:14px;max-height:20px;max-width:64px;">
            <span></span>
          </div>
        </div>
        <div id="mic1" i="1" class="fl mic dots border corner bg" style="background-size: cover;background-position: center;">
          <div class="u" style="display: flex;width: 100%;background: rgba(222, 222, 222, 0.35);margin-top: 26px;">
            <img class="object-fit" style="min-height:14px;max-height:20px;max-width:64px;">
            <span></span>
          </div>
        </div>
        <div id="mic2" i="2" class="fl mic dots border corner bg" style="background-size: cover;background-position: center;">
          <div class="u" style="display: flex;width: 100%;background: rgba(222, 222, 222, 0.35);margin-top: 26px;">
            <img class="object-fit" style="min-height:14px;max-height:20px;max-width:64px;">
            <span></span>
          </div>
        </div>
        <div id="mic3" i="3" class="fl mic dots border corner bg" style="background-size: cover;background-position: center;">
          <div class="u" style="display: flex;width: 100%;background: rgba(222, 222, 222, 0.35);margin-top: 26px;">
            <img class="object-fit" style="min-height:14px;max-height:20px;max-width:64px;">
            <span></span>
          </div>
        </div>
        <div id="mic4" i="4" class="fl mic dots border corner bg" style="background-size: cover;background-position: center;">
          <div class="u" style="display: flex;width: 100%;background: rgba(222, 222, 222, 0.35);margin-top: 26px;">
            <img class="object-fit" style="min-height:14px;max-height:20px;max-width:64px;">
            <span></span>
          </div>
        </div>
    
      </div>
      <div id="d2" class="d2  flex-grow-1 light    break">
      </div>
      <div class="tablebox d-flex footer fl light" style="width:100%;height:42px;padding:4px;">
        <button onclick="send('rleave',{});" style=" margin-left:-2px;margin-top:2px;border-radius: 2px;"
          class="fa fa-sign-out fl btn btn-primary">&nbsp;</button>
        <img   role="button" class="fl nosel emobox"
          style="padding:5px;width:34px;height: 34px;" dsrc="imgs/emoii.gif">
        <textarea id="tbox"
          onclick="setTimeout(function(){$('#d2').scrollTop($('#d2')[0].scrollHeight);},600);"
          placeholder="اكتب رسالتك هنا" class="fl tbox flex-fill" style="background-color: rgb(170, 170, 175);"></textarea>
        <button onclick="Tsend();" style=" margin-left:2px;" class="fa fa-send fl btn btn-primary">إرسال</button>
      </div>
      <div id="d0" class="nosel d-flex fl  bg" style="padding-left: 1px; margin-top: 1px; width: 100%;  ">
        <button title="المتواجدين" href="#"
          onclick="$('#dpnl').show();$('#dpnl').find('.pnhead').text($(this).attr('title'));setTimeout(function(){$('#users').scrollTop(0);},100);$('#usearch').val('');$('#rsearch').val('');"
           data-target="#users" class="ae fa label label-primary fa-user" style="width: 18%;"><span
            id="busers" style="padding:1px 4px;"></span></button>
        <button title="المحادثات الخاصه" href="#"
          onclick="$('#dpnl').find('.pnhead').text($(this).attr('title'));hl('#pmb','primary');setTimeout(function(){$('#users').scrollTop(0);},100);$('#dpnl').show();"
           data-target="#chats" class="ae fa chats label label-primary fa-comment"
          style="width: 21%;" id="pmb"><span style="padding:1px 4px;"></span>خاص</button>
        <button title="غرف الدردشه" id="brooms" href="#"
          onclick="$('#dpnl').find('.pnhead').text($(this).attr('title'));$('#usearch').val('');$('#rsearch').val('');$('#dpnl').show();" 
          data-target="#rooms" class="ae fa label label-primary fa-users" style="width: 19%;"><span
            style="padding:1px 4px;"></span>الغرف</button>
        <button title="الحائط" href="#"
          onclick="$('#dpnl').find('.pnhead').text($(this).attr('title'));$('#dpnl').show();setTimeout(function(){$('#d2bc').scrollTop(0);});bcc=0;$(this).css('color', '').find('#bwall').text('');"
            data-target="#wall" class="ae fa label label-primary fa-comments"><span id="bwall"
            style="padding:1px 4px;"></span>الحائط</button>
        <button title="الإعدادات" href="#" onclick="$('#dpnl').find('.pnhead').text($(this).attr('title'));$('#dpnl').show();"
            data-target="#settings" class="ae label label-primary fa fa-gear" style="width: 18%;"><span
            style="padding:1px 4px;"></span>الضبط</button><span class="flex-grow-1"></span>
 
      </div>
    </div>
    <div id="dpnl" class="dpnl bg break"
      style="display:none;border: 1px solid;max-width: 340px;min-width: 300px; top: 0px;right: 0px;">
      <div style="width: 100%;">
        <label onclick="$(this).parent().parent().hide();$('#dpnl .x').css('display','none')"   data-target="#settings"
          class="label label-danger border nosel fa fa-close fr"
          style="margin-bottom: 0px;margin:1px;border: 1px solid black;padding:6px 12px;">&nbsp;</label>
        <label class="fl nosel label pnhead" style="margin:3px;padding-left:10px;padding-right:10px;">المتواجدين</label>
      </div>
      <div id="users" style="width:100%;position: absolute;top:32px;bottom:0px;display: none;" class="x light break">
        <textarea type="search" id="usearch" placeholder=".. البحث" class="tbox bg border"
          style="width:100%;padding-left:5px;display:block;border-radius: 0px;"></textarea>
        <label style=" margin: 0px!important;width:100%;margin:0px;border:none;padding:4px;border-radius:0px;display:none;"
          v="0" n="10000" class="nosel ninr   fl uzr label bg">المتواجدين في الدردشه</label>
      </div>
      <div id="chats" style="width:100%;position: absolute;top:32px;bottom:0px;display: none;" class="x break light">
      </div>
      <div id="wall" style="width:100%;position: absolute;top:32px;bottom:0px;display: none;" class="x break">
        <div style="width: 100%;height: 100%;" class="c-flex">
          <div id="d2bc" class="d2 light  d2bc flex-grow-1   break  " style="width:100%;">
            <button class="fa fa-commentsx btn border btn-info" onclick="$(this).parent().scrollTop(0);$(this).hide();"
              style="display:none;position: absolute;top: 0px;z-index: 1;width: 100%;text-align: center;"
              id="bcmore">رسائل جديدة</button>
          </div>
          <div class="tablebox light d-flex" style="width:100%;height:42px;padding:4px;">
            <button onclick="sendbc(true);" style="margin-left:2px; margin-right:3px;margin-top:2px;border-radius: 2px;"
              class="fr fa fa-share-alt sndfilebc fl btn btn-primary"></button>
            <img role="button" class="fl emobox"
              style="padding:5px;width:34px;height: 34px;" dsrc="imgs/emoii.gif">
            <textarea placeholder="اكتب رسالتك هنا" class="fl tbox tboxbc flex-fill"></textarea>
            <button onclick="sendbc();" style="margin-left:2px;" class="fa fa-send sndbc fl btn btn-primary">إرسال</button>
          </div>
        </div>

      </div>
      <div id="rooms" style="width:100%;position: absolute;top:32px;bottom:0px;display: none;" class="x light break">
        
        <div id="roomss" style="width:100%;margin:0px;border:none;border-radius:0px;" class="nosel d-flex label-primary fl  bgg">
          <textarea type="search" id="rsearch" placeholder="البحث عن رقم الغرفه #.." class="flex-grow-1 tbox bg border"
            style="height:37px!important;padding-left:5px;display:inline;border-radius: 0px;"></textarea>
          <button onclick="mkr();" class="border btn label label-success fr   fa fa-plus " style="margin:1px;">غرفه جديدة</button>
            
        </div>
      </div>
      
 
      <div id="settings" style="width:100%;padding:0px;position: absolute;top:32px;bottom:0px;display: none;" class="x break light ">
        <data class="c-flex break" style="width: 100%;height: 100%;">
        
        <div class="borderg c-flex corner" style="background-color: white;width: 100%;">

          <div class="d-flex">
            <img style="width: 150px;height: 100%;background-size: contain;background-position: center center; ;background-color: #272727;" class="fitimg flex-grow-1  borderg spic   hand">
            <div>
              <div class="d-flex" style="padding: 1px;margin-top:2px;height:32px;">
                <div class="cpick border scolor btn"
                style="width: 38px;margin-left:auto;  color: rgb(0, 0, 0); background-image: none; background-color: rgb(255, 255, 255);">
              </div>
                <div style="display: inline-block;width: 90px;margin-left: 1px;" class=" nosel  label label-primary">لون الإسم</div>
              </div>
              <div class="d-flex" style="padding: 1px;height:32px;">
                <div class="cpick border mcolor btn"
                style="width: 38px;margin-left:auto;  color: rgb(0, 0, 0); background-image: none; background-color: rgb(255, 255, 255);">
              </div>
                <div style="display: inline-block;width: 90px;;margin-left: 1px;" class="  nosel label label-primary">لون الخط</div>
              </div>
              <div class="d-flex" style="padding: 1px;height:32px;">
                <div class="cpick border sbg btn"
                style="width: 38px;margin-left:auto;  color: rgb(0, 0, 0); background-image: none; background-color: rgb(255, 255, 255);">
              </div>
                <div style="display: inline-block;width: 90px;padding:1px;margin-left: 1px;" class="nosel   label label-primary">لون الخلفيه</div>
              </div>
          
            </div>
          </div>
              <div class="d-flex" style="padding: 1px;">
            <textarea class="stopic flex-grow-1" style="height:48px;padding:4px;resize: none;"></textarea>
            <div style="display: inline-block;width: 90px;margin-left: 1px;height:32px;" class="nosel label label-primary  ">الزخرفه</div>
          </div>
          <div class="d-flex" style="padding: 1px;">
            <textarea  class="smsg flex-grow-1" style="height:64px;max-height: 64px; resize: none;"></textarea>
            <div style="display: inline-block;width: 90px;margin-left: 1px;height:32px;" class=" nosel  label label-primary">الحاله</div>
          </div>
          <span id="ping" class="fr mini nosel" style="text-align: center;padding: 4px;margin-top: -30px;width: 90px;min-width: 90px;height: 26px;margin-left: auto;"></span>
          <button class="border btn mini btn-success hand  "
            style="padding:6px;width: 98%;margin-left:3px;margin-top: 8px;margin-bottom: 4px;" onclick="setprofile();">
            <span class="  fa fa-edit"></span>
            حـفـظ التغيرات</button>
        </div>
    
        <div class="c-flex flex-grow-1">
          <select id="zoom" style="width: 98%;margin:1px 4px;" class="  btn btn-primary"
          onchange="document.body.style.zoom=$(this).val();setv('zoom',$(this).val());fixSize();">
          <option selected="selected" value="1">%100 - حجم الخطوط</option>
          <option value="1.20">%120 - حجم الخطوط</option>
          <option value="1.10">%110 - حجم الخطوط</option>
          <option value="1.05">%105 - حجم الخطوط</option>
          <option value="0.95">%95 - حجم الخطوط</option>
          <option value="0.9">%90 - حجم الخطوط</option>
        </select>
        <select id="micq" style="width: 98%;margin:1px 4px;display: none;" class="btn btn-primary">
          <option value="3">48kb عاليه</option> 
          <option value="2" selected >32kb جودة المايك</option>
          <option value="4">16kb ضعيفه</option>
        </select>

        
 
        <label onclick="if (nopm){nopm=false;$(this).find('span').first().removeClass('fa-ban');}else{nopm=true;$(this).find('span').first().addClass('fa-ban');};send('busy',{busy:nopm});"
        style="background-color: ghostwhite;color: black;margin:1px 4px; padding:6px;"
        class="label tc border  btn "><span class="fa fl"></span><span class="fa fr fa-comment" style="padding:0px 4px;"></span>تعطيل المحادثات الخاصه</label>
        <label onclick="if (nonot){nonot=false;$(this).find('span').first().removeClass('fa-ban');}else{nonot=true;$(this).find('span').first().addClass('fa-ban');} ;"
        style="background-color: ghostwhite;color: black;margin:1px 4px; padding:6px;"
        class="label tc border  btn  bb4 "><span class="fa fl"></span><span class="fa fr fa-envelope-o" style="padding:0px 4px;"></span>تعطيل التنبيهات</label>
        <label onclick="if (nowall){nowall=false;$(this).find('span').first().removeClass('fa-ban');}else{nowall=true;$(this).find('span').first().addClass('fa-ban');} ;"
        style="background-color: ghostwhite;color: black;margin:1px 4px; padding:6px;"
        class="label tc border  btn  bb4 "><span class="fa fl"></span><span class="fa fr fa-comments" style="padding:0px 4px;"></span>تعطيل إشعارات الحائط</label>
 
        <div class="flex-grow-1"></div>
      <label onclick="pmsg();" style="background-color: ghostwhite;color: black;margin:1px 4px; padding:6px;"
        class="label tc border pmsg  btn  label-info "><span class="fl fa fa-send"></span>إرسال إعلان</label>
     
      <label onclick="if(myroom!=null){redit(myroom);}"
        style="background-color: ghostwhite;color: black;margin:1px 4px; padding:6px;"
        class="label tc border redit  btn  label-info "><span class="fl fa fa-home"></span>إداره الغرفه</label>
      
      <label rel="opener" target="_blank" style="background-color: ghostwhite;color: black;margin:1px 4px; padding:6px;"
        class="label tc border cp  btn  label-danger"><span class="fl fa fa-star"></span>لوحه التحكم</label>
      
      <label onclick="logout();" style="margin:1px 4px; padding:6px;"
        class="label border btn  label-danger tc"><span class="fl fa fa-sign-out"></span>تسجيل خروج</label>

        </div>
      </data>
 
 
 
    
       
    
      </div>
    </div>
    <h6 class="hid">
      شات الجوال.شات جوال,شات كتابي,شات للجوال,شات جوال الخليج, دردشة الجوال ، شات الجوال الكتابي ، شات جوال ، الجوال,شات دردشة جوال ، شات جوال للجوال ، شات الجوال الصوتي ، شات دردشة الجوال ، شات دردشة جوال ، شات الجوال الصوتي ، شات الجوال العربي ، شات الجوال الخليج شات دردشة الجوال
    </h6> 
     <!-- upro Modal -->
     <div class="modal" id="upro" role="dialog" style="z-index:2100">
      <div class="modal-dialog ">
        <div class="modal-content" style="width:340px;margin:-1px;">
          <div style="color:white;margin-top:-1px;" onclick="$(this).parent().parent().parent().modal('hide');"
            class="modal-header label-primary">
            <span class="pull-right clickable badge"><i class="fa fa-times"></i></span>
            <label style="margin:1px;max-width:90%;" class="mini dots nosel modal-title">إنشاء غرفه جديدة</label>
          </div>
          <div class="modal-body" style="padding:0px;">
            <div class="light fl pro" style="width:100%; padding:0px;margin:0px;">
              <div style="width:100%;height:220px;background-size: cover!important;" class="fitimg fl u-pic2"> 
                <div  style="width:100%;height:220px;background-size: contain!important;backdrop-filter: blur(4px) brightness(0.8);-webkit-backdrop-filter: blur(4px) brightness(0.8);" class="fitimg fl u-pic "> 
                </div>
              </div> 
              <label style="width:100%;text-align:end;margin-bottom:0px;" class=""> 
                <div class="fl mini u-co" style="margin:4px;"></div>
                <div class="fr u-room dots" style="margin: 2px;"></div>
                <div style="width:100%;padding:2px;text-align: center;" class="fl u-msg"></div>
              </label>
              <span class="fl fa fa-comment  btn upm borderg"
                style="color:black;margin:2px;width: 108px;text-align: center;outline: 1px solid #0000001f;">محادثه خاصه</span>
              <span class="fl fa fa-envelope-o btn unot borderg"
                style="color:black;margin:2px;width: 108px;text-align: center;outline: 1px solid #0000001f;">تنبيه</span>
              <span class="fl fa fa-heart btn ulike borderg"
                style="margin:2px;color:red;width: 108px;text-align: center;outline: 1px solid #0000001f;">0</span>
 
                
              <span class="fl fa fa-ban btn umute borderg"
                style="color:red;margin:2px;margin-top:4px;width: 108px;text-align: center;outline: 1px solid #0000001f;">تجاهل</span>
              <span class="fl fa fa-check btn uunmute borderg"
                style="color:red;margin:2px;margin-top:4px;width: 108px;text-align: center;outline: 1px solid #0000001f;">إلغاء التجاهل</span>
                <span style="width: 112px;height:33px;" class="fl"></span>
                <span class="fl fa fa-star btn uadmin borderg"
                style="color:blue;margin:2px;margin-top:4px;margin-bottom:8px;width: 108px;text-align: center;outline: 1px solid #0000001f;">إداره</span>


              <div class="borderg d-flex nickbox fl" style="margin-top:2px;width:100%;padding: 1px;">
                <span class="fl" style="padding: 4px 8px;min-width: 70px;">الزخرفه</span>
                <textarea class="border flex-grow-1 fl u-topic"
                  style="height:33px;padding:4px;resize:none;"></textarea>
                <span class="btn border u-nickc fr fa fa-check">حفظ</span>
              </div>
              <div class="borderg d-flex fl likebox" style="width:100%;margin-top:2px;padding: 1px;">
                <span style="color:red;padding: 4px 2px;min-width: 70px;">♥الايكات</span>
                <input type="number" style="height: 33px;width: inherit;" class="flex-grow-1 likec">
                <span class="fa fr fa-check btn ulikec border" style="min-width: 70px;">حفظ</span>
              </div>
              <div class="borderg fl powerbox" style="width:100%;padding: 1px;margin-top:2px;">
                <label class="fl" style="width: 70px;">المجموعه</label>
                <select style="width: 184px;display:inline;" class="userpower fl selbox form-control"></select> 
                <br><br>
                <label class="fl" style="width: 70px;">المده بالأيام</label>
                <input type="number" class="userdays fl" style="width: 44px;padding: 4px;">
                <input id="upsearch" class="fl" style="width:140px;padding: 4px;" placeholder="البحث في الصلاحيات">
                <span class="fa fr fa-check btn upower border">حفظ</span>
    
              </div>
              <div class="borderg fl roomzbox" style="width:100%;padding: 1px;margin: 8px 0px;">
                <label class="fl" style="min-width: 70px;text-align: center;">الغرفه</label>
                <select style="width:184px;display:inline;" class="roomz fl selbox form-control"></select> 
                <span class="fa fr fa-check btn uroomz border" style="min-width:70px">نقل</span> 
              </div> 
            </div>
          </div>
        </div>
      </div>
    </div>
    <x id="uhtml" style="display:none;">
      <div class="fl hand nosel uzr uhtml">
        <div class="d-flex fl di1">
          <img class="ustat" alt="">
          <div class="fitimg u-pic">
          </div> 
          <div class="di2 flex-grow-1 break">
            <div class="d-flex dots">
              <span class="muted fa"></span>
              <img class="u-ico" alt="">
              <div class="flex-grow-1"><span
                  class="u-topic"></span></div>
            </div>
            <div class="mini u-msg"></div>
          </div>
        </div>
        <div class="di3 c-flex fr">
          <img class="co ico" alt="" onerror="if((this.src||'').split('/').pop()!='--.png'){this.src='flags/--.png';};">
          <span class="uhash"></span>
        </div>
      </div>
    </x>
    <x id="umsg" style="display:none;">
      <div class="uzr d-flex"
        style="background-color: #fafafa;outline:1px solid lavender;margin:1px;margin-bottom: 0px;width:100%;padding:0px;">
        <div style="min-width:54px;width:54px;min-height:48px;max-height:62px;background-position: center;background-size: auto;"
          class="fitimg u-pic">
        </div>
        <div class="uzr flex-fill break" style="padding-top: 1px;padding-left:1px;">
          <div style="width:100%;display: flex;height: 21px;" class="d-flex">
           <div class="d-flex flex-grow-1" style="overflow: hidden;"> <img class="u-ico" style="min-height: 14px;">
            <span style="padding:1px 4px;display:block;margin-left:1px;border-radius: 2px;"
              class="nosel u-topic dots hand"></span></div>
            <div class="flex-grow-1 d-flex" style="height: 21px;overflow-y: visible;"> 
              <span class="flex-grow-1 "></span> 
              <span style="padding:0px 2px;margin-right:4px;color:grey;text-align: end;"
                class="minix tago">..</span>
            </div>
          </div>
          <div style="padding:0px 5px; width:100%;padding-right:1px;padding-bottom: 1px;" class="u-msg break"></div>
          <div class="d-flex fr" style="max-width: 92px;height: 21px;margin-left: auto;">
            <button style="padding:3px;width: 28px;"
            class="blike corner btn minix btn-danger fa fa-heart"></button>
          <button style="padding:3px;width: 28px;"
            class="breply corner btn minix btn-primary fa fa-comments"></button>
          <button style="padding:3px;width: 26px;"
            class="bdel corner btn minix btn-primary fa fa-times"></button>
          </div> 
          <div class="reply break " style="max-height:0px;">
          </div>
        </div>
      </div>
    </x> 
    <x id="rhtmlx" style="display:none;">
      <div class="nosel d-flex room"
        style="text-align:left;width:100%; padding:0px;background-color: #fafafa;outline:1px solid lavender;margin:1px;margin-bottom: 0px;">
            <div style="width:100%;height:140px;background-size: auto;border: 0px;background-color: #2020201f;background-position: center center;" class="d-flex fitimg u-pic">
              <div class="hash" style="color:#fafafa;text-align: left;padding: 0px 4px;background-color: #2020204f;height: 20px;"></div>
              <div class="st flex-grow-1" style="color:white;text-align: left;height: 20px;"></div>
              <div class="d-flex">
              

                <span  style="display: none; color:whitesmoke;background-color: #20202008;padding-left:2px;"
                class="u-topic dots flex-grow-1"></span> 
                <span class="uc"
                style="z-index:10;padding:1px;border-radius: 0px;min-width: 28px;text-align: center;margin-left:auto;margin-right:1px;height: 21px;background-color:#2020204f;color:white;"></span>
              </div> 
              <div style="display: none;color:#888; text-align: left;padding-left:4px; background-color: #dfdfdf1f;" class="mini u-msg flex-grow-1"></div>

                 </div> 
      </div>
    </x> 
        <x id="rhtml" style="display:none;">
      <div class="nosel d-flex room"
        style="text-align:left;width:100%; padding:0px;background-color: #fafafa;outline:1px solid lavender;margin:1px;margin-bottom: 0px;">
            <div style="width:64px;min-width:64px;min-height:50px;max-height:110px;background-size: auto;border: 0px;background-color: #2020201f;background-position: center center;" class="d-flex fitimg u-pic">
              
              <div class="hash" style="color:#fafafa;text-align: left;padding: 0px 1px;background-color: #2020205f;height: 20px;margin-left: auto;"></div>
            </div>
            <div class="c-flex flex-grow-1 break"  style="overflow: hidden;">
              <div class="d-flex">
                <div class="st" style="color:white;text-align: right;height: 20px;min-width: 38px;font-size: 14.5px!important;"></div>
                <span  
                class="u-topic dots flex-grow-1"></span> 
                <span class=" uc"
                style="z-index:10;padding:1px;border-radius: 0px;min-width: 32px;text-align: center;margin-left:auto;height: 21px;"></span>
              </div> 
                 
                <div style="color:#888; text-align: left;padding-left:4px; " class="u-msg flex-grow-1"></div>
                 
              

            </div>  
        
      </div>
    </x>

    
    <x id="uhead" style="display:none;">
      <div class="fl d-flex uzr flex-grow-1 dots" style="margin:3px;">
        <img class="fl ustat" style='width:4px;height:22px;' dsrc='imgs/s1.png'>
        <img style="width:36px;height:36px;background-size: cover;background-position: center;" class="fitimg fl hand u-pic ">
        <div style="width:100%;margin-top:0px;" class="fl dots d-flex flex-grow-1">
          <img class="fl u-ico" alt="">
          <span style="max-width:100%;padding: 0px 4px;border-radius: 0px;margin-left:1px;"
            class=" nosel flex-grow-1 u-topic dots">{1}</span>
        </div>
      </div>
    </x>
    <x id="pop" class="hid">
      <div class="bgg corner c-flex"
        style="outline: 1px solid gray;overflow-y:hidden;display:none;position:absolute;top:1px;min-height:180px;max-height:500px;max-height:70%;max-width:420px;width:100%;z-index:10;">
        <div style="width:100%;height:30px;" class="head nosel bg fl">
          <label class="label fl hand fa fa-info title" style="margin:2px;margin-right: 2px;">&nbsp;</label>
          <label style="padding:8px;" onclick="$(this).parent().parent().remove();"
            class="btn minix btn-danger pphide fr border fa fa-close">&nbsp;&nbsp;</label>
        </div>
        <div class="body fl flex-grow-1 break" style="width:100%;overflow: auto;">
        </div>
      </div>
    </x>
    <x id="cw" class="hid">
      <div class="c-flex bgg border"
        style="border-radius: 2px;display:none;position:absolute;top:1px;min-height:190px;max-height:500px;bottom:46%;width:99.8%;max-width:500px; ">
        <div style="width:100%;height:30px;" class="head d-flex nosel bg fl">
          <label class="label fl hand fa border fa-user" style="margin:2px;margin-right: 2px;">&nbsp;</label>
          <span class="uhash" style="color: white;padding: 2px;"></span>
          <label style="padding:8px;"
            onclick="var pp=$(this).parent().parent();if($(this).hasClass('fa-expand')){pp.css('bottom','114px');}else{pp.css('bottom','46%');}$(this).toggleClass('fa-expand fa-compress');fixSize();"
            class="btn   btn-info   fr border fa fa-expand">&nbsp;&nbsp;</label>
          <label style="padding:8px;" class="btn minix btn-danger phide fr border fa fa-minus">&nbsp;&nbsp;</label>
        </div>
        <div class="cont break fr c-flex flex-grow-1" style="width:100%;">
          <div class="d2 flex-grow-1 break light">
          </div> 
          <div class="tablebox footer d-flex light fl" style="width:100%;height:42px;padding:4px;">
            <div class="typ" style="display: flex;position: absolute;right:20px;margin-top:-16px;">
              <div class="xtyping"></div>
              <div class="xtyping"></div>
              <div class="xtyping"></div>
            </div>
            <span class="btn btn-success fa fa-phone callx" style="margin-right:3px;margin-top:2px;border-radius: 2px;"></span>
            <button style="margin-right:3px;margin-top:2px;border-radius: 2px;"
              class="fr fa fa-share-alt sndfile fl btn btn-primary">&nbsp;&nbsp;&nbsp;&nbsp;</button>
            <img role="button" class="fl nosel emobox"
              style="padding:5px;width:34px;height: 34px;" dsrc="imgs/emoii.gif">
            <textarea placeholder="اكتب رسالتك هنا" class="fl flex-fill tbox"></textarea>
            <button style="margin-left:2px;" class="fa fa-send sndpm fl btn btn-primary">&nbsp;&nbsp;&nbsp;</button>
          </div>
        </div>
      </div>
    </x>
    <div id="call" class="c-flex border bg  call" style="display:none;position: fixed;
    width: 260px;
    z-index: 9000;
    top: 55px;
    left: 5px;padding:1px; padding-top:0px;"> 
          <div class="fl d-flex uzr flex-grow-1 dots" style="margin:0px;background-color: #fafafa;"> 
            <img style="width:32px;min-width:32px;height:28px;background-size: cover;background-position: center;" class="fitimg fl hand u-pic ">
            <div style="width:100%;margin-top:0px;" class="fl dots d-flex flex-grow-1">
              <img class="fl u-ico" alt="">
              <span style="max-width:100%;padding: 0px 4px;border-radius: 0px;margin-left:1px;"
                class=" nosel flex-grow-1 u-topic dots"></span>
            </div>
          </div>
          <div class="d-flex" id="callv" style="height:3px; flex: 0 1 auto;background-color:#00ff0000;"></div>
<div class="d-flex">
  <span class="btn btn-danger fa fa-phone" style="padding:4px 24px;margin-right:2px;"></span>
  <span class="btn btn-success fa fa-phone" style="padding:4px 24px;">
  
  </span><span  class=" flex-grow-1 "></span>
  <span id="callt" class="label flex-grow-1 nosel stat"></span> 
  <span class="btn btn-primary fa fa-microphone-slash" style="padding:4px 24px;"></span>
</div>

  </div>
    <x id="not" class="hid">
      <div onclick="$(this).remove();"
        style="min-height:50px;min-width:180px;padding:4px;max-width:260px;border:1px solid black;z-index:9999;background-color:#efefef;position:absolute;top:30%;margin-left:30px;"
        class="hand corner nosel"> 
      </div>
    </x>
    <div class="modal" id="mnot" role="dialog">
      <div class="modal-dialog ">
        <div class="modal-content" style="width:310px;">
          <div style="color:white;" onclick="$(this).parent().parent().parent().modal('hide') ;"
            class="modal-header label-primary">
            <span class="pull-right clickable badge"><i class="fa fa-times"></i></span>
            <label style="margin:1px;" class="mini fa fa-comments modal-title">إعلان</label>
          </div>
          <div class="modal-body" style="padding:1px;">
            <div class="break" style="background-color:#efefef;padding:5px;">
              <textarea placeholder="اكتب رسالتك هنا" class="fl tbox" style="width:100%;resize: none;height:48px;max-height:48px;"></textarea>
              <img role="button" class="fl nosel emobox"
              style="padding:5px;width:34px;height: 34px;" dsrc="imgs/emoii.gif">
              <label class="checkbox-inline" style="padding-top:6px;"><input class="ispp" type="checkbox" value="">إعلان خاص للسوابر؟</label><button
                class="rsave btn btn-primary fr"><span class="fa fa-send">إرسال</span></button>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="modal" id="mmnot" role="dialog" style="z-index: 99999;">
      <div class="modal-dialog ">
        <div class="modal-content" style="width:310px;">
          <div style="color:white;" onclick="$(this).parent().parent().parent().modal('hide') ;"
            class="modal-header label-primary">
            <span class="pull-right clickable badge"><i class="fa fa-times"></i></span>
            <label style="margin:1px;" class="mini fa fa-comments modal-title">تنبيه</label>
          </div>
          <div class="modal-body" style="padding:1px;">
            <div class="break" style="background-color:#efefef;padding:5px;">
              <textarea placeholder="اكتب رسالتك هنا" class="fl tbox" style="width:100%;resize: none;height:48px;max-height:48px;"></textarea>
              <img role="button" class="fl nosel emobox"
              style="padding:5px;width:34px;height: 34px;" dsrc="imgs/emoii.gif">
              <button class="rsave btn btn-primary fr"><span class="fa fa-send">إرسال</span></button>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- mkr Modal -->
    <div class="modal" id="mkr" role="dialog">
      <div class="modal-dialog ">
        <div class="modal-content" style="width:310px;">
          <div style="color:white;" onclick="$(this).parent().parent().parent().modal('hide') ;"
            class="modal-header label-primary">
            <span class="pull-right clickable badge"><i class="fa fa-times"></i></span>
            <label style="margin:1px;" class="mini fa fa-comments modal-title">إنشاء غرفه جديدة</label>
          </div>
          <div class="modal-body" style="padding:1px;">
            <div class="break" style="background-color:#efefef;padding:5px;">
              <input class="rtopic" style="width:200px;" type="text" placeholder="عنوان الغرفه">
              <input class="rabout" style="width:200px;" type="text" placeholder="الوصف">
              <input class="rwelcome" style="width:200px;" type="text" placeholder="رساله الترحيب">
              <input autocomplete="off" class="rpwd" style="width:200px;" type="text" placeholder="كلمه المرور">
              <input class="rmax" style="width:200px;" type="number" placeholder="حجم الغرفه من 2 ألى 40" min="2" max="40">
              <input class="rl" style="width:200px;" type="number" placeholder="عدد الايكات للدخول" min="0">
              <input class="rvl" style="width:200px;" type="number" placeholder="عدد الايكات للصوتيه" min="0">
              <br>
              <label class="checkbox-inline"><input class="rbnr" type="checkbox" value="">تفعيل البنر</label><br>
              <label class="checkbox-inline"><input class="rev" type="checkbox" value="">بدون إشعارات الدخول</label><br>
              <label class="checkbox-inline"><input class="rnos" type="checkbox" value="">منع المخفي</label><br>
              <label class="checkbox-inline"><input class="rv" type="checkbox" value="">تفعيل الصوتيه</label><br>
              <label class="checkbox-inline"><input class="rdel" type="checkbox" value="">تثبيت الغرفه</label><br>
              <br>
              <div class=" border  btn" style="width: 84px;position: absolute;top: 96px;right: 4px;padding: 4px;" onclick="$('#mkr .rpic').css('background-image','url(room.webp)').attr('src','room.webp')">بدون صوره</div>
              <div class="cpick border  btn"
                style="width: 84px;height: 44px;color: rgb(0, 0, 0);background-image: none;position: absolute;top: 140px;right: 4px;">
              </div> 
              <button class="rmake btn btn-primary fl"><span class="fa fa-plus">إنشاء الغرفه</span></button>
              <button class="rsave btn btn-primary fl"><span class="fa fa-edit">حفظ التعديلات</span></button>
              <button class="rdelete btn btn-danger fr"><span class="fa fa-times">حذف</span></button>
              <div class="rpic fr border" onclick="roomspic($(this));"> 
              </div>
              <div class="break border corner" id="ops" style="width:100%;padding:2px;">
              </div>
              <div class="break border corner" id="rbans" style="width:100%;padding:2px;margin-top:4px;">
                <span class="fl border" style="width:100%;"><button style="width:38px;" class="rbansx btn fr btn-danger fa fa-times"></button>المحظورين</span>
                <div class="bans" style="width: 100%;"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>  
    <div class="modal break" id="rpl" role="dialog" style="margin:5px;">
      <div class="modal-dialog fr break" style="height: 50%;min-height: 300px;width:306px;overflow: visible;margin-top:2px;margin-right: 6px;">
        <div class="modal-content c-flex break" style="width:306px;height: 100%;">
          <div style="color:white;" onclick="$(this).parent().parent().parent().modal('hide') ;"
            class="modal-header label-primary">
            <span class="pull-right clickable badge"><i class="fa fa-times"></i></span>
            <label style="margin:1px;" class="mini fa fa-comments modal-title">التعليقات</label>
          </div>
          <div class="modal-body break flex-grow-1 c-flex" style="padding:0px;">
            <div style="width: 100%;border-bottom: 1px solid black;" class="rmsg">
            </div>
            <div class="break flex-grow-1 r" style="background-color:#efefef;">
            </div>
            <div class="tablebox footer d-flex light fl" style="width:100%;height:42px;padding:4px;"> 
              <img role="button" class="fl nosel emobox"
                style="padding:5px;width:34px;height: 34px;" dsrc="imgs/emoii.gif">
              <textarea placeholder="اكتب رسالتك هنا" class="fl flex-fill tbox"></textarea>
              <button style="margin-left:2px;" class="fa fa-send sndpm fl btn btn-primary">&nbsp;&nbsp;&nbsp;</button>
            </div>
          </div>
        </div>
      </div>
    </div> 
    <h6 class="hid">
      شات الجوال
    </h6> 
    <span id="tdwi" style="display: none;">
<span style="text-wrap: nowrap;color:#e700b2;">AN</span>.<span style="text-wrap: nowrap;color:#db001d;">Chfx</span>|<span style="text-wrap: nowrap;color:#ce0021;">2dd.dd.dddd</span>.<span style="text-wrap: nowrap;color:#c20025;">2xxxxx.d</span>
</span>
<div id="cp" class="break d-flex"
  style="width:100%;height:100%;position: absolute;top:0px;left:0px;overflow-x: auto;zoom: 95%;display: none;background-color: #fefefe;">
  <div style="margin:1px;" class="fl borderg" id='m1'>
    <ul class="nav nav-pills nav-stacked" style="margin-top: 0px;">
      <li><a data-toggle="tab" href="#fps">السجل</a></li>
      <li><a data-toggle="tab" href="#actions">الحالات</a></li>
      <li><a data-toggle="tab" href="#logins">الأعضاء</a></li>
      <li><a data-toggle="tab" href="#bans">الحظر</a></li>
      <li><a data-toggle="tab" href="#powers">الصلاحيات</a></li>
      <li><a data-toggle="tab" href="#fltr">فلتر</a></li>
      <li><a data-toggle="tab" href="#cp_rooms">الغرف</a></li>
      <li><a data-toggle="tab" href="#shrt">الإختصارات</a></li>
      <li><a data-toggle="tab" href="#subs">الإشتراكات</a></li>
      <li><a data-toggle="tab" href="#msgs">الرسائل</a></li>
      <li><a data-toggle="tab" href="#wrooms">غرفه الإنتظار</a></li>
      <li><a data-toggle="tab" href="#stats">إحصائيات</a></li>
      <li><a data-toggle="tab" href="#sett">إداره الموقع</a></li>
      <li><a data-toggle="tab" href="#domains">النطاقات</a></li>
    </ul> 
  </div>
  <div class="fl  tab-content" style="height:100%;width:100%;min-width:200px;margin-left:0px;min-height: 100%;">
    <button class="fa fa-refresh btn btn-success" onclick="$('#m1 .active a').click();"
      style="position: fixed;top: 6px;right: 8px;"></button>
    <div id='actions' class="tab-pane cpactions">
      <button disabled class="fa fa-arrow-left btn btn-primary" style="width: 60px;"></button>
      <button disabled class="fa fa-arrow-right btn btn-primary" style="width: 60px;text-align: end;"></button>
      <input autocomplete="off" placeholder="البحث" value="" onchange="send('cp',{cmd:'actions',q:$(this).val()});">
    </div>
    <div id='fps' class="tab-pane fade  cpevents  ">
      <button disabled class="fa fa-arrow-left btn btn-primary" style="width: 60px;"></button>
      <button disabled class="fa fa-arrow-right btn btn-primary" style="width: 60px;text-align: end;"></button>
      <input autocomplete="off" placeholder="البحث" value="" onchange="send('cp',{cmd:'fps',q:$(this).val().trim()});">
    </div>
    <div id='logins' class="tab-pane fade cpactions" style="width: 100%;">
      <button disabled class="fa fa-arrow-left btn btn-primary" style="width: 60px;"></button>
      <button disabled class="fa fa-arrow-right btn btn-primary" style="width: 60px;text-align: end;"></button>
      <input autocomplete="off" placeholder="البحث اسم العضو\الآي بي\الجهاز"
        onchange="send('cp',{cmd:'logins',q:$(this).val().trim()});">
    </div>
    <div id='bans' class="tab-pane  bans">
      <div style="width: 150%;"> <input autocomplete="off" class="banit" placeholder="رقم الجهاز \\ الدوله \\ الاي بي">
        <button onclick="send('cp',{cmd:'ban',type:$('.banit').val().trim()});$('.banit').val('')"
          class="btn btn-danger">حظر</button>
        <button onclick="send('cp',{cmd:'aban',type:$('.banit').val().trim()});$('.banit').val('')"
          class="btn btn-success">سماح</button>
      </div>
    </div>
    <div id='powers' class="tab-pane" style="width:500px;">
      <input onchange="cp_powers();" id="psearch" style="width:200px;display: block;margin-bottom: 2px;"
        placeholder="البحث في الصلاحيات">
      <select onchange="cp_powerchange();" style="width:200px;display:inline" class="powerbox selbox form-control">
      </select>
      <button class="delp btn btn-danger fa fa-times">حذف</button>
      <div class="sico border fr" style="width:220px;margin-top:1px;margin-right: 4px;">

      </div>
    </div>
    <div id='fltr' class="tab-pane fltr">
      <input class="fltrit" placeholder="اضافه كلمه\موقع">
      <br><button style="margin:5px;width:200px;" onclick="fltrit('amsgs',$('.fltrit').val());"
        class="fa fa-check btn btn-success">إضافه ألى الكلمات المسموحه</button>
      <br><button style="margin:5px;width:200px;" onclick="fltrit('bmsgs',$('.fltrit').val());"
        class="fa fa-times btn btn-danger">إضافه ألى الكلمات الممنوعه</button>
      <br><button style="margin:5px;width:200px;" onclick="fltrit('wmsgs',$('.fltrit').val());"
        class="fa fa-warning btn btn-info">إضافه ألى الكلمات المراقبه</button>
      <br>
      <div style="min-width:380px;height:260px;display: inline-block;" class="flcont break borderg">

      </div>
    </div>
    <div id='cp_rooms' class="tab-pane">

    </div>

    <div id='shrt' class="tab-pane">
      <input style="margin: 4px;" class="shrtname" placeholder="الإختصار \ س1">
      <br><input style="margin: 4px;" class="shrtvalue" placeholder="الزخرفه \ السلام عليكم">
      <br><button style="margin:5px;width:200px;"
        onclick="send('cp',{cmd:'shrtadd',name:$('.shrtname').val(),value:$('.shrtvalue').val()});"
        class="fa fa-check btn btn-success">إضافه</button>
      <br>
    </div>
    <div id="stats" class="tab-pane">

    </div>
    <div id="subs" class="tab-pane">

    </div>
    <div id='msgs' class="tab-pane msgs" style="padding:2px;">
      <input class="msgt" style="width: 200px;" placeholder="عنوان الرساله">
      <br>
      <textarea class="msgm" style="width: 200px;" placeholder="الرساله" maxlength="250"></textarea>
      <br><button style="margin:5px;width:200px;"
        onclick="send('cp',{cmd:'msgsit',type:'w',t:$('.msgt').val(),m:$('.msgm').val()});"
        class="fa fa-check btn btn-success">إضافه ألى رسائل الترحيب</button>
      <br><button style="margin:5px;width:200px;"
        onclick="send('cp',{cmd:'msgsit',type:'d',t:$('.msgt').val(),m:$('.msgm').val()});"
        class="fa fa-check btn btn-danger">إضافه ألى الرسائل التلقائيه</button>
      <br><b>الرسائل</b><br>
    </div>
    <div id="wrooms" class="tab-pane">
      <label class="label label-primary">غرفه الانتظار</label>
      <br>
      <input id="owr_active" type="checkbox" class="dots" autocomplete="off">
      <label for="owr_active" class="checkbox-inline">تفعيل غرفه الانتظار</label>
      <br>
      <input id="owr_move" type="checkbox" class="dots" autocomplete="off">
      <label for="owr_move" class="checkbox-inline">نقل تلقائي للغرف العامه بعد اكتمال الايكات</label>
      <br>
      <input id="owr_pm" type="checkbox" class="dots" autocomplete="off">
      <label for="owr_pm" class="checkbox-inline">منع الخاص</label>
      <br>
      <input id="owr_allowlikes" type="checkbox" class="dots" autocomplete="off">
      <label for="owr_allowlikes" class="checkbox-inline">منع الايكات</label>
      <br>
      <input id="owr_nots" type="checkbox" class="dots" autocomplete="off">
      <label for="owr_nots" class="checkbox-inline">منع التنبيهات</label>
      <br>
      <input id="owr_autoAllow" type="checkbox" class="dots" autocomplete="off">
      <label for="owr_autoAllow" class="checkbox-inline">سماح تلقائي بعد اكتمال الايكات</label>
      <br>
      <input id="owr_likes" type="number" min="1" value="0" class="dots" style="width: 80px;" autocomplete="off"><b>عدد
        الايكات لمغادره غرفه الانتظار</b>
      <br>
      <label id="owr_save" class="btn btn-danger label fa fa-save border">حفظ</label>
      <br>
      <br>
      <div style="width: 150%;"> <input autocomplete="off" class="wroomit"
          placeholder="رقم الجهاز \\ الدوله \\ الاي بي">
        <button onclick="send('cp',{cmd:'wrooms+',type:$('.wroomit').val().trim()});$('.wroomit').val('')"
          class="btn btn-danger">إضافه</button>
        <button onclick="send('cp',{cmd:'wroomsa',type:$('.wroomit').val().trim()});$('.wroomit').val('')"
          class="btn btn-success">سماح</button>
      </div>
    </div>
    <div id='sett' class="tab-pane fade">
      <div class="border corner" style="padding:4px;">
        <label class="label label-info fa fa-home">إعدادات الموقع</label>
        <br>
        <label class="label label-primary">إسم الموقع</label>
        <br>
        <textarea maxlength="5120" style="width:260px;" id="sett_name" type="text"
          placeholder="اسم الموقع: شات كتابي"></textarea>
        <br>
        <label class="label label-primary">عنوان الصفحه</label>
        <br>
        <textarea maxlength="5120" style="width:260px;" id="sett_title" type="text"
          placeholder="عنوان الصفحه:شات كتابي"></textarea>
        <br>
        <label class="label label-primary">وصف الموقع</label>
        <br>
        <textarea maxlength="5120" style="width:260px;" id="sett_description" type="text"
          placeholder="وصف الموقع: مثال شات كتابي"></textarea>
        <br>
        <label class="label label-primary">الكلمات الدلاليه</label>
        <br>
        <textarea maxlength="5120" style="width:260px;" id="sett_keywords" type="text"
          placeholder="الكلمات الدلاليه: كلمه1,كلمه2,كلمه3"></textarea>
        <br>
        <label class="label label-primary">السكربت JavaScript</label>
        <br>
        <textarea maxlength="10240" style="width:260px;" id="sett_scr" type="text"
          placeholder="للمبرمجين فقط"></textarea>
        <br>
        <label class="label label-primary">الوصف الداخلي html SEO</label>
        <br>
        <textarea maxlength="10240" style="width:310px;height: 100px;" id="sett_html" type="text" placeholder="شات الكتابي
<div>شات الكتابي</div>
<a href='/'>شات الكتابي</a>"></textarea>
        <br>
        <label class="label label-primary">لون القوالب</label>
        <br>
        <input class="jscolor color {pickerPosition:'top'} sbg dots"
          style="width: 80px; color: rgb(255, 255, 255); background-image: none; background-color: rgb(0, 0, 0);"
          autocomplete="off">
        <br>
        <label class="label label-primary">لون المحتوى</label>
        <br>
        <input class="color {pickerPosition:'top'} sbackground dots"
          style="width: 80px; color: rgb(255, 255, 255); background-image: none; background-color: rgb(0, 0, 0);"
          autocomplete="off">
        <br>
        <label class="label label-primary">لون الأزرار</label>
        <br>
        <input class="color {pickerPosition:'top'} sbuttons dots"
          style="width: 80px; color: rgb(255, 255, 255); background-image: none; background-color: rgb(0, 0, 0);"
          autocomplete="off">
        <br>
        <label class="label label-primary">الرسائل</label>
        <br> <input type="number" min="5" value="5" class="msgstt dots" style="width: 80px;" autocomplete="off"><b>المده
          بالدقائق للرسائل التلقائيه</b>
        <br>
        <label class="label label-primary">الحائط</label>
        <br><input type="number" min="0" value="0" class="wall_likes dots" style="width: 80px;"
          autocomplete="off"><b>عدد الايكات</b>

        <br> <input type="number" min="0" value="0" class="wall_minutes dots" style="width: 80px;"
          autocomplete="off"><b>المده بين رسائل الحائط بالدقيقه</b>

        <br>
        <label class="label label-primary">الزوار</label>
        <br>
        <input id="allowg" type="checkbox" class="allowg dots" autocomplete="off">
        <label for="allowg" class="checkbox-inline">السماح بدخول الزوار</label>
        <br>
        <input id="allowreg" type="checkbox" class="allowreg corner dots" autocomplete="off">
        <label for="allowreg" class="checkbox-inline">السماح بتسجيل العضويات</label>
        <br>
        <input id="rc" type="checkbox" class="rc dots" autocomplete="off">
        <label for="rc" class="checkbox-inline">إستعاده الاتصال</label>
        <br>
        <input id="bclikes" type="checkbox" class="dots" autocomplete="off">
        <label for="bclikes" class="checkbox-inline">ﻻيكات الحائط</label>
        <br>
        <input id="mlikes" type="checkbox" class=" dots" autocomplete="off">
        <label for="mlikes" class="checkbox-inline">ﻻيكات العام</label>
        <br>
        <input id="mreply" type="checkbox" class=" dots" autocomplete="off">
        <label for="mreply" class="checkbox-inline">ردود العام</label>
        <br>
        <input id="bcreply" type="checkbox" class=" dots" autocomplete="off">
        <label for="bcreply" class="checkbox-inline">ردود الحايط</label>
        <br>
        <br><input type="number" min="0" value="0" class="pmlikes dots" style="width: 80px;" autocomplete="off"><b>عدد
          الايكات للمحادثات الخاصه</b>
        <br><input type="number" min="0" value="0" class="notlikes dots" style="width: 80px;" autocomplete="off"><b>عدد
          الايكات للتنبيهات</b>
        <br><input type="number" min="0" value="0" class="fileslikes dots" style="width: 80px;"
          autocomplete="off"><b>عدد الايكات لإرسال صور\فيديو</b>
        <br><input type="number" min="0" value="0" class="proflikes dots" style="width: 80px;" autocomplete="off"><b>عدد
          الايكات لتغير الزخرفه والحاله</b>
        <br><input type="number" min="0" value="0" class="piclikes dots" style="width: 80px;" autocomplete="off"><b>عدد
          الايكات لتغير الصوره</b>
        <br><input type="number" min="1" value="5" max="10" class="maxshrt dots" style="width: 80px;"
          autocomplete="off"><b>عدد الاختصارت في الرساله الواحده</b>
        <br><input type="number" min="2" value="5" class="maxIP dots" style="width: 80px;" autocomplete="off"><b>تعدد
          النكات من نفس الجهاز</b>
        <br><input type="number" min="1" min="240" value="1" class="stay dots" style="width: 80px;"
          autocomplete="off"><b>تثبيت النكات بعد انقطاع النت</b>
        <br>
        <input id="likeTax" type="checkbox" class=" dots" autocomplete="off">
        <label for="likeTax" class="checkbox-inline">تقليص الايكات❤️ بنسبه %1 كل ساعتين</label>
        <br>
        <input id="calls" type="checkbox" class=" dots" autocomplete="off">
        <label for="calls" class="checkbox-inline">تفعيل المكالمات</label>
        <br><input type="number" min="0" value="0" class="callsLike dots" style="width: 80px;"
          autocomplete="off"><b>الايكات المطلوبه لإجراء مكالمات</b>
        <br>

        <label class="btn btn-danger label fa fa-save border" onclick="sett_save();">حفظ</label>
        <br>
      </div>
      <div class="border" style="padding:4px;margin-top:-1px;">
        <label class="label label-primary">ملفات توثيق Google</label>
        <br>
        <input style="width:240px;" id="verGoog" value="" autocomplete="off"
          placeholder="مثال google4c13fc6f4ff7bbc4.html">
        <label class="btn  verGoog label btn-success fa fa-plus border"
          onclick="send('cp',{cmd:'goog+',v:$('#verGoog').val()});$('#verGoog').val('');"
          style="padding: 2px 4px;">إضافه</label>
        <br>
        <label class="label label-info fa fa-gear">خيارات الموقع</label>
        <br>
        <label class="label label-primary">الايقونات</label>
        <div style="width:360px;" class="p-site break">
          <label class="label label-primary">ايقونه الموقع favicon.ico</label><br>
          <div
            style="width:48px;height:48px;background-image:url(favicon.ico);background-size: cover;background-position: center;"
            onclick="sendfilea(this,function(fn){send('cp',{cmd:'favicon',fn:fn});},'pic?k=favicon&');"></div>
          <label class="label label-primary">صوره الموقع prv1</label><br>
          <div
            style="width:48px;height:48px;background-image:url(prv1.webp);background-size: cover;background-position: center;"
            onclick="sendfilea(this,function(fn){send('cp',{cmd:'prv1',fn:fn});},'pic?k=prv1&');"></div>
          <label class="label label-primary">صوره العضو pic</label><br>
          <div
            style="width:48px;height:48px;background-image:url(pic.webp);background-size: cover;background-position: center;"
            onclick="sendfilea(this,function(fn){send('cp',{cmd:'pic',fn:fn});},'pic?k=pic&');"></div>
          <label class="label label-primary">صوره الغرف room</label><br>
          <div
            style="width:54px;height:48px;background-image:url(room.webp);background-size: cover;background-position: center;"
            onclick="sendfilea(this,function(fn){send('cp',{cmd:'room',fn:fn});},'pic?k=room&');"></div>
 
        </div>
        <br>
        <label class="label label-primary fa fa-image">ايقونات السوابر</label><span
          class="btn btn-primary minix fa fa-plus border b-sico"></span>
        <div style="width:360px;" class="p-sico break">
        </div>
        <br>
        <label class="label label-primary fa fa-image">ايقونات الهدايا</label><span
          class="btn btn-primary minix fa fa-plus border b-dro3"></span>
        <div style="width:360px;" class="p-dro3 break">
        </div>
        <br>
        <label class="label label-primary fa fa-image">الإبتسامات</label><span
          class="btn btn-primary minix fa fa-plus border b-emo"></span><span
          class="btn emo_order btn-primary minix fa fa-save border">حفظ</span>
        <div style="width:360px;" class="p-emo break">
        </div>
      </div>
    </div>
    <div id='domains' class="tab-pane fade">
      <div class="border corner" style="padding:4px;">
        <label class="label label-success fa fa-home">النطاقات</label>
        <br>
        <select style="width:200px;display:inline" id="domain_list" class="form-control">
        </select> <label class="btn btn-danger label fa fa-times border"
          onclick="send('cp',{cmd:'domaindel',d:$('#domain_list').val()});">إزاله</label>
        <br>
        <label id="domain_status" class=""></label>
        <br>
        <label class="label label-primary">إسم النطاق site.com?</label>
        <br>
        <input maxlength="5120" style="width:260px;" id="domain" type="text" placeholder="site.com">
        <br>
        <label class="label label-primary">إسم الموقع</label>
        <br>
        <textarea maxlength="5120" style="width:260px;" id="domain_name" type="text"
          placeholder="اسم الموقع: شات كتابي"></textarea>
        <br>
        <label class="label label-primary">عنوان الصفحه</label>
        <br>
        <textarea maxlength="5120" style="width:260px;" id="domain_title" type="text"
          placeholder="عنوان الصفحه:شات كتابي"></textarea>
        <br>
        <label class="label label-primary">وصف الموقع</label>
        <br>
        <textarea maxlength="5120" style="width:260px;" id="domain_description" type="text"
          placeholder="وصف الموقع: مثال شات كتابي"></textarea>
        <br>
        <label class="label label-primary">الكلمات الدلاليه</label>
        <br>
        <textarea maxlength="5120" style="width:260px;" id="domain_keywords" type="text"
          placeholder="الكلمات الدلاليه: كلمه1,كلمه2,كلمه3"></textarea>
        <br>
        <label class="label label-primary">السكربت JavaScript</label>
        <br>
        <textarea maxlength="10240" style="width:260px;" id="domain_scr" type="text"
          placeholder="للمبرمجين فقط"></textarea>
        <br>
        <label class="label label-primary">الوصف الداخلي html SEO</label>
        <br>
        <textarea maxlength="10240" style="width:310px;height: 100px;" id="sett_htmld" type="text" placeholder="شات الكتابي
<div>شات الكتابي</div>
<a href='/'>شات الكتابي</a>"></textarea>
        <br>
        <label class="label label-primary">لون القوالب</label>
        <br>
        <input class="jscolor color {pickerPosition:'top'} domain_sbg dots"
          style="width: 80px; color: rgb(255, 255, 255); background-image: none; background-color: rgb(0, 0, 0);"
          autocomplete="off">
        <br>
        <label class="label label-primary">لون المحتوى</label>
        <br>
        <input class="color {pickerPosition:'top'} domain_sbackground dots"
          style="width: 80px; color: rgb(255, 255, 255); background-image: none; background-color: rgb(0, 0, 0);"
          autocomplete="off">
        <br>
        <label class="label label-primary">لون الأزرار</label>
        <br>
        <input class="color {pickerPosition:'top'} domain_sbuttons dots"
          style="width: 80px; color: rgb(255, 255, 255); background-image: none; background-color: rgb(0, 0, 0);"
          autocomplete="off">
        <br>
        <label class="btn btn-danger label fa fa-save border" onclick="domains_save();">حفظ</label>
        <br>
      </div>
    </div>
  </div>
</div>
    </body>  
<script src="b.js"></script>       
<script src="x5.js?29"></script>     

</html>