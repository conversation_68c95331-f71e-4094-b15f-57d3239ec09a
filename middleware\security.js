const rateLimit = require('express-rate-limit');
const { decryptCommand } = require('../utils/encryption');

// Rate limiting for WebSocket connections
const socketRateLimit = new Map();

/**
 * Rate limiting middleware for Socket.IO
 */
function socketRateLimiter(socket, next) {
    const clientIp = socket.handshake.address;
    const now = Date.now();
    const windowMs = 60000; // 1 minute
    const maxConnections = 10; // Max 10 connections per minute per IP
    
    if (!socketRateLimit.has(clientIp)) {
        socketRateLimit.set(clientIp, []);
    }
    
    const connections = socketRateLimit.get(clientIp);
    
    // Remove old connections
    const validConnections = connections.filter(time => now - time < windowMs);
    
    if (validConnections.length >= maxConnections) {
        const error = new Error('Rate limit exceeded');
        error.data = { type: 'rate_limit', retry_after: windowMs };
        return next(error);
    }
    
    validConnections.push(now);
    socketRateLimit.set(clientIp, validConnections);
    
    next();
}

/**
 * Message rate limiting for individual sockets
 */
class MessageRateLimiter {
    constructor() {
        this.userLimits = new Map();
    }
    
    checkLimit(userId, messageType = 'text') {
        const now = Date.now();
        const windowMs = 60000; // 1 minute
        
        // Different limits for different message types
        const limits = {
            text: 30,      // 30 text messages per minute
            voice: 100,    // 100 voice packets per minute
            admin: 60      // 60 admin commands per minute
        };
        
        const maxMessages = limits[messageType] || limits.text;
        
        if (!this.userLimits.has(userId)) {
            this.userLimits.set(userId, []);
        }
        
        const userMessages = this.userLimits.get(userId);
        
        // Remove old messages
        const validMessages = userMessages.filter(time => now - time < windowMs);
        
        if (validMessages.length >= maxMessages) {
            return false;
        }
        
        validMessages.push(now);
        this.userLimits.set(userId, validMessages);
        
        return true;
    }
    
    cleanup() {
        const now = Date.now();
        const windowMs = 60000;
        
        for (const [userId, messages] of this.userLimits) {
            const validMessages = messages.filter(time => now - time < windowMs);
            if (validMessages.length === 0) {
                this.userLimits.delete(userId);
            } else {
                this.userLimits.set(userId, validMessages);
            }
        }
    }
}

/**
 * Input validation and sanitization
 */
function validateMessage(data) {
    if (!data || typeof data !== 'object') {
        return { valid: false, error: 'Invalid message format' };
    }
    
    const { cmd, data: messageData } = data;
    
    if (!cmd || typeof cmd !== 'string') {
        return { valid: false, error: 'Invalid command format' };
    }
    
    // Decrypt and validate command
    try {
        const decryptedCmd = decryptCommand(cmd);
        
        // Check if command is allowed
        const allowedCommands = [
            'hi', 'ping', 'online', 'rc', 'bc', 'join', 'leave', 
            'voice', 'cp', 'cpi', 'logout'
        ];
        
        if (!allowedCommands.includes(decryptedCmd)) {
            return { valid: false, error: 'Unknown command' };
        }
        
        // Validate message data based on command
        switch (decryptedCmd) {
            case 'bc':
                return validateBroadcastMessage(messageData);
            case 'join':
                return validateJoinRoom(messageData);
            case 'cp':
                return validateAdminCommand(messageData);
            default:
                return { valid: true };
        }
    } catch (error) {
        return { valid: false, error: 'Command decryption failed' };
    }
}

function validateBroadcastMessage(data) {
    if (!data || typeof data !== 'object') {
        return { valid: false, error: 'Invalid message data' };
    }
    
    const { msg, link, bid } = data;
    
    if (!msg || typeof msg !== 'string') {
        return { valid: false, error: 'Message text required' };
    }
    
    if (msg.length > 2000) {
        return { valid: false, error: 'Message too long' };
    }
    
    if (link && (typeof link !== 'string' || link.length > 500)) {
        return { valid: false, error: 'Invalid link format' };
    }
    
    if (bid && typeof bid !== 'string') {
        return { valid: false, error: 'Invalid reply ID format' };
    }
    
    // Check for spam patterns
    if (isSpamMessage(msg)) {
        return { valid: false, error: 'Message flagged as spam' };
    }
    
    return { valid: true };
}

function validateJoinRoom(data) {
    if (!data || typeof data !== 'object') {
        return { valid: false, error: 'Invalid room data' };
    }
    
    const { roomId, password } = data;
    
    if (!roomId || typeof roomId !== 'string' || roomId.length > 50) {
        return { valid: false, error: 'Invalid room ID' };
    }
    
    if (password && (typeof password !== 'string' || password.length > 100)) {
        return { valid: false, error: 'Invalid password format' };
    }
    
    return { valid: true };
}

function validateAdminCommand(data) {
    if (!data || typeof data !== 'object') {
        return { valid: false, error: 'Invalid admin command data' };
    }
    
    const { cmd } = data;
    
    if (!cmd || typeof cmd !== 'string') {
        return { valid: false, error: 'Admin command required' };
    }
    
    const allowedAdminCommands = [
        'powers', 'subs', 'rooms', 'logins', 'bots', 'bans', 
        'domains', 'shrt', 'actions', 'msgs', 'wrooms', 
        'stats', 'owner', 'fps', 'fltr', 'sico'
    ];
    
    if (!allowedAdminCommands.includes(cmd)) {
        return { valid: false, error: 'Unknown admin command' };
    }
    
    return { valid: true };
}

function isSpamMessage(message) {
    // Basic spam detection patterns
    const spamPatterns = [
        /(.)\1{10,}/,                    // Repeated characters
        /https?:\/\/[^\s]+/gi,           // Multiple URLs
        /\b(free|win|prize|click)\b/gi,  // Spam keywords
        /[A-Z]{5,}/g                     // Excessive caps
    ];
    
    return spamPatterns.some(pattern => pattern.test(message));
}

/**
 * Fingerprint validation
 */
function validateFingerprint(fingerprint) {
    if (!fingerprint || typeof fingerprint !== 'string') {
        return false;
    }
    
    // Basic fingerprint format validation
    return fingerprint.length >= 10 && fingerprint.length <= 1000;
}

/**
 * IP-based security checks
 */
function checkIPSecurity(ip) {
    // Check against known malicious IPs
    const blockedIPs = [
        '127.0.0.1', // Example blocked IP
    ];
    
    if (blockedIPs.includes(ip)) {
        return { allowed: false, reason: 'IP blocked' };
    }
    
    // Check for private/local IPs in production
    if (process.env.NODE_ENV === 'production') {
        const privateIPRegex = /^(10\.|172\.(1[6-9]|2[0-9]|3[01])\.|192\.168\.)/;
        if (privateIPRegex.test(ip)) {
            return { allowed: false, reason: 'Private IP not allowed in production' };
        }
    }
    
    return { allowed: true };
}

/**
 * Clean up rate limiting data periodically
 */
setInterval(() => {
    const now = Date.now();
    const windowMs = 60000;
    
    // Clean up socket rate limits
    for (const [ip, connections] of socketRateLimit) {
        const validConnections = connections.filter(time => now - time < windowMs);
        if (validConnections.length === 0) {
            socketRateLimit.delete(ip);
        } else {
            socketRateLimit.set(ip, validConnections);
        }
    }
}, 60000); // Clean up every minute

module.exports = {
    socketRateLimiter,
    MessageRateLimiter,
    validateMessage,
    validateFingerprint,
    checkIPSecurity,
    isSpamMessage
};
