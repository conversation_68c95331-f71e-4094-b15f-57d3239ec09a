# نظام الدردشة الفورية - Backend

نظام دردشة فورية متقدم مبني بـ Node.js و Socket.IO و MongoDB، يدعم الدردشة النصية والصوتية ولوحة التحكم الإدارية.

## المميزات الرئيسية

### 🔐 الأمان والتشفير
- خوارزمية تشفير franzetta (XOR-based) متوافقة مع Frontend
- حماية من الـ Rate Limiting
- فلترة الرسائل والحماية من الـ Spam
- نظام Fingerprinting للأجهزة

### 💬 نظام الدردشة
- دردشة نصية فورية متعددة الغرف
- دعم الردود على الرسائل
- نظام الإشارات (@mentions)
- فلترة المحتوى التلقائية

### 🎤 الدردشة الصوتية
- نقل الصوت عبر WebSocket (Binary)
- دعم متعدد المستخدمين في الغرفة الواحدة
- مؤشرات بصرية لمن يتكلم
- تحكم في الصوت (كتم/إلغاء كتم)

### 🏠 إدارة الغرف
- إنشاء وإدارة الغرف الديناميكية
- غرف عامة وخاصة (محمية بكلمة مرور)
- نظام المشرفين والصلاحيات
- إحصائيات الغرف والمستخدمين

### 👥 إدارة المستخدمين
- نظام المستخدمين الضيوف والمسجلين
- ملفات شخصية قابلة للتخصيص
- نظام النقاط والسمعة
- تتبع النشاط والحضور

### 🛠️ لوحة التحكم الإدارية
- واجهة إدارية شاملة (`/cp`)
- إدارة المستخدمين والصلاحيات
- مراقبة الرسائل والنشاط
- إحصائيات مفصلة
- سجلات الأنشطة الإدارية

## متطلبات النظام

- Node.js 16.0.0 أو أحدث
- MongoDB 4.4 أو أحدث
- ذاكرة RAM: 512MB كحد أدنى
- مساحة تخزين: 1GB كحد أدنى

## التثبيت والتشغيل

### 1. تحميل التبعيات
```bash
npm install
```

### 2. إعداد قاعدة البيانات
تأكد من تشغيل MongoDB على النظام:
```bash
# Ubuntu/Debian
sudo systemctl start mongod

# Windows
net start MongoDB

# macOS
brew services start mongodb-community
```

### 3. إعداد متغيرات البيئة
انسخ ملف `.env` وقم بتعديل الإعدادات:
```bash
cp .env.example .env
```

قم بتعديل الإعدادات في `.env`:
```env
# إعدادات الخادم
PORT=3000
NODE_ENV=development

# إعدادات قاعدة البيانات
MONGODB_URI=mongodb://localhost:27017/schat

# إعدادات الأمان
JWT_SECRET=your-super-secret-jwt-key
ENCRYPTION_KEY=franzetta-encryption-key-2024
```

### 4. تشغيل الخادم
```bash
# للتطوير
npm run dev

# للإنتاج
npm start
```

## هيكل المشروع

```
├── config/
│   └── database.js          # إعدادات قاعدة البيانات
├── controllers/
│   ├── SocketController.js  # تحكم في WebSocket
│   └── AdminController.js   # تحكم في لوحة الإدارة
├── middleware/
│   └── security.js          # وسطاء الأمان
├── models/
│   ├── User.js              # نموذج المستخدم
│   ├── Room.js              # نموذج الغرفة
│   ├── Message.js           # نموذج الرسالة
│   └── AdminLog.js          # نموذج سجلات الإدارة
├── utils/
│   ├── encryption.js        # وظائف التشفير
│   └── helpers.js           # وظائف مساعدة
├── public/                  # الملفات الثابتة (Frontend)
├── server.js                # الخادم الرئيسي
└── package.json
```

## API والأوامر

### أوامر WebSocket الأساسية

#### 1. الاتصال الأولي
```javascript
// إرسال
{
  "cmd": "hi",
  "data": "encrypted_handshake_data"
}

// استقبال
{
  "cmd": "hi", 
  "data": "encrypted_response"
}
```

#### 2. تسجيل الدخول
```javascript
// إرسال
{
  "cmd": "online",
  "data": {
    "p": "user_fingerprint"
  }
}

// استقبال
{
  "cmd": "ok",
  "data": {
    "k": "user_token"
  }
}
```

#### 3. إرسال رسالة
```javascript
{
  "cmd": "bc",
  "data": {
    "msg": "نص الرسالة",
    "link": "رابط اختياري",
    "bid": "معرف_الرسالة_المرد_عليها"
  }
}
```

#### 4. الانضمام لغرفة
```javascript
{
  "cmd": "join",
  "data": {
    "roomId": "معرف_الغرفة",
    "password": "كلمة_المرور_اختيارية"
  }
}
```

### أوامر لوحة التحكم

#### الحصول على قائمة المستخدمين
```javascript
{
  "cmd": "cp",
  "data": {
    "cmd": "powers"
  }
}
```

#### الحصول على إحصائيات النظام
```javascript
{
  "cmd": "cp",
  "data": {
    "cmd": "stats"
  }
}
```

## الأمان والحماية

### 1. التشفير
- جميع الأوامر مشفرة بخوارزمية franzetta
- التشفير متوافق 100% مع Frontend الموجود
- حماية البيانات الحساسة

### 2. Rate Limiting
- حد أقصى 30 رسالة نصية في الدقيقة
- حد أقصى 100 حزمة صوتية في الدقيقة
- حد أقصى 10 اتصالات جديدة في الدقيقة لكل IP

### 3. فلترة المحتوى
- فلترة الكلمات المحظورة
- كشف الرسائل المكررة (Spam)
- فلترة الروابط المشبوهة

### 4. مراقبة النشاط
- تسجيل جميع الأنشطة الإدارية
- تتبع عناوين IP والـ Fingerprints
- إنذارات الأنشطة المشبوهة

## الصيانة والمراقبة

### تنظيف البيانات التلقائي
- حذف الرسائل القديمة (30 يوم افتراضياً)
- إزالة المستخدمين غير النشطين
- تنظيف سجلات الإدارة القديمة

### مراقبة الأداء
```bash
# عرض حالة الخادم
curl http://localhost:3000/api/health

# عرض الإحصائيات
curl http://localhost:3000/api/stats
```

### النسخ الاحتياطي
```bash
# نسخ احتياطي لقاعدة البيانات
mongodump --db schat --out backup/

# استعادة النسخة الاحتياطية
mongorestore --db schat backup/schat/
```

## استكشاف الأخطاء

### مشاكل شائعة

#### 1. خطأ في الاتصال بقاعدة البيانات
```bash
# تحقق من تشغيل MongoDB
sudo systemctl status mongod

# تحقق من الاتصال
mongo --eval "db.adminCommand('ismaster')"
```

#### 2. مشاكل في التشفير
- تأكد من تطابق خوارزمية franzetta مع Frontend
- تحقق من إعدادات ENCRYPTION_KEY

#### 3. مشاكل الأداء
- راقب استخدام الذاكرة والمعالج
- تحقق من فهارس قاعدة البيانات
- راجع سجلات الأخطاء

## المساهمة والتطوير

### إضافة ميزات جديدة
1. أنشئ فرع جديد للميزة
2. اتبع معايير الكود الموجودة
3. أضف اختبارات للميزات الجديدة
4. حدث الوثائق

### اختبار النظام
```bash
# تشغيل الاختبارات
npm test

# تشغيل اختبارات التغطية
npm run test:coverage
```

## الترخيص

هذا المشروع مرخص تحت رخصة MIT. راجع ملف LICENSE للتفاصيل.

## الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل:
- أنشئ Issue في GitHub
- راسل فريق التطوير
- راجع الوثائق التقنية
