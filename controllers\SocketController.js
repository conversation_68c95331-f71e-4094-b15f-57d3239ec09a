const { v4: uuidv4 } = require('uuid');
const { franzetta, encryptCommand, decryptCommand, generateEncryptedToken } = require('../utils/encryption');
const User = require('../models/User');
const Room = require('../models/Room');
const Message = require('../models/Message');
const AdminLog = require('../models/AdminLog');
const AdminController = require('./AdminController');

class SocketController {
    constructor(socket, io, globalData) {
        this.socket = socket;
        this.io = io;
        this.connectedUsers = globalData.connectedUsers;
        this.userSockets = globalData.userSockets;
        this.roomUsers = globalData.roomUsers;
        this.voiceStreams = globalData.voiceStreams;
        
        this.user = null;
        this.isAuthenticated = false;
        this.lastPing = Date.now();
        this.messageQueue = [];
        this.rateLimitData = {
            messages: [],
            lastReset: Date.now()
        };

        // Initialize admin controller
        this.adminController = new AdminController(socket, io, globalData);
    }

    async handleMessage(data) {
        try {
            const { cmd, data: messageData } = data;
            
            // Decrypt command
            const decryptedCmd = decryptCommand(cmd);
            
            console.log(`📨 Received command: ${decryptedCmd} from ${this.socket.id}`);
            
            switch (decryptedCmd) {
                case 'hi':
                    await this.handleHandshake(messageData);
                    break;
                case 'ping':
                    await this.handlePing(messageData);
                    break;
                case 'online':
                    await this.handleOnline(messageData);
                    break;
                case 'rc':
                    await this.handleReconnect(messageData);
                    break;
                case 'bc':
                    await this.handleBroadcastMessage(messageData);
                    break;
                case 'join':
                    await this.handleJoinRoom(messageData);
                    break;
                case 'leave':
                    await this.handleLeaveRoom(messageData);
                    break;
                case 'voice':
                    await this.handleVoiceCommand(messageData);
                    break;
                case 'cp':
                    await this.handleAdminCommand(messageData);
                    break;
                case 'cpi':
                    await this.handleAdminMessage(messageData);
                    break;
                case 'logout':
                    await this.handleLogout(messageData);
                    break;
                default:
                    console.log(`❓ Unknown command: ${decryptedCmd}`);
                    break;
            }
        } catch (error) {
            console.error('❌ Error handling message:', error);
            this.sendError('Invalid message format');
        }
    }

    async handleHandshake(data) {
        try {
            // Generate encrypted response
            const response = franzetta(data);
            
            // Send encrypted handshake response
            this.socket.send(JSON.stringify({
                cmd: 'hi',
                data: response
            }));
            
            console.log(`🤝 Handshake completed for ${this.socket.id}`);
        } catch (error) {
            console.error('❌ Handshake error:', error);
            this.socket.disconnect();
        }
    }

    async handlePing(timestamp) {
        try {
            this.lastPing = Date.now();
            
            // Send pong response
            this.sendEncrypted('pong', timestamp);
            
            // Update user activity if authenticated
            if (this.user) {
                await this.user.updateActivity();
            }
        } catch (error) {
            console.error('❌ Ping error:', error);
        }
    }

    async handleOnline(data) {
        try {
            const { p: fingerprint } = data;
            
            // Create or find guest user
            const userId = uuidv4();
            const userData = {
                id: userId,
                topic: `زائر_${Math.floor(Math.random() * 9999)}`,
                socketId: this.socket.id,
                isOnline: true,
                fingerprint: fingerprint || null,
                lastSeen: new Date(),
                lupd: new Date()
            };
            
            // Save user to database
            this.user = await User.create(userData);
            
            // Store in memory
            this.connectedUsers.set(this.socket.id, this.user);
            this.userSockets.set(userId, this.socket.id);
            
            this.isAuthenticated = true;
            
            // Generate token
            const token = generateEncryptedToken();
            
            // Send success response
            this.sendEncrypted('ok', { k: token });
            
            // Send initial data
            await this.sendInitialData();
            
            console.log(`✅ User ${this.user.topic} connected`);
        } catch (error) {
            console.error('❌ Online error:', error);
            this.sendEncrypted('nok', { error: 'Connection failed' });
        }
    }

    async handleReconnect(data) {
        try {
            const { token, n: userId, rct: roomToken } = data;
            
            // Find existing user
            const existingUser = await User.findOne({ id: userId });
            if (!existingUser) {
                this.sendEncrypted('nok', { error: 'User not found' });
                return;
            }
            
            // Update user connection
            existingUser.socketId = this.socket.id;
            existingUser.isOnline = true;
            existingUser.lastSeen = new Date();
            existingUser.lupd = new Date();
            await existingUser.save();
            
            this.user = existingUser;
            this.isAuthenticated = true;
            
            // Store in memory
            this.connectedUsers.set(this.socket.id, this.user);
            this.userSockets.set(userId, this.socket.id);
            
            // Send success response
            this.sendEncrypted('ok', { k: token });
            
            // Send initial data
            await this.sendInitialData();
            
            console.log(`🔄 User ${this.user.topic} reconnected`);
        } catch (error) {
            console.error('❌ Reconnect error:', error);
            this.sendEncrypted('nok', { error: 'Reconnection failed' });
        }
    }

    async sendInitialData() {
        try {
            // Send user list
            const users = await User.findOnlineUsers();
            const userList = users.map(u => u.toClientFormat());
            this.sendEncrypted('ulist', userList);
            
            // Send room list
            const rooms = await Room.findActiveRooms();
            const roomList = rooms.map(r => r.toClientFormat());
            this.sendEncrypted('rlist', roomList);
            
            // Send emojis and other static data
            this.sendEncrypted('emos', []); // Will be populated from database
            this.sendEncrypted('dro3', []); // Will be populated from database
            this.sendEncrypted('sico', []); // Will be populated from database
            
            // Send user powers if admin
            if (this.user.powers && this.user.powers.length > 0) {
                this.sendEncrypted('powers', this.user.powers);
            }
        } catch (error) {
            console.error('❌ Error sending initial data:', error);
        }
    }

    async handleBroadcastMessage(data) {
        try {
            if (!this.isAuthenticated || !this.user) {
                this.sendError('Not authenticated');
                return;
            }
            
            // Rate limiting check
            if (!this.checkRateLimit()) {
                this.sendError('Rate limit exceeded');
                return;
            }
            
            const { msg, link, bid } = data;
            
            // Validate message
            if (!msg || msg.trim() === '' || msg === '\n' || msg === '%0A' || msg === '%0a') {
                return; // Ignore empty messages
            }
            
            if (msg.length > 2000) {
                this.sendError('Message too long');
                return;
            }
            
            // Check if user is in a room
            if (!this.user.roomid) {
                this.sendError('Not in a room');
                return;
            }
            
            // Find the room
            const room = await Room.findOne({ id: this.user.roomid, isActive: true });
            if (!room) {
                this.sendError('Room not found');
                return;
            }
            
            // Check if user is banned from room
            if (room.isUserBanned(this.user.id)) {
                this.sendError('You are banned from this room');
                return;
            }
            
            // Create message
            const messageId = uuidv4();
            const messageData = {
                id: messageId,
                msg: msg.trim(),
                link: link || null,
                bid: bid || null,
                userId: this.user.id,
                userTopic: this.user.topic,
                userPic: this.user.pic,
                userBg: this.user.bg,
                userColor: this.user.ucol,
                userIco: this.user.ico,
                roomId: this.user.roomid,
                ip: this.socket.handshake.address,
                fingerprint: this.user.fingerprint
            };
            
            // Handle reply
            if (bid) {
                const replyToMessage = await Message.findOne({ id: bid });
                if (replyToMessage) {
                    messageData.replyTo = {
                        messageId: replyToMessage.id,
                        userId: replyToMessage.userId,
                        userTopic: replyToMessage.userTopic,
                        preview: replyToMessage.msg.substring(0, 100)
                    };
                }
            }
            
            // Save message to database
            const message = await Message.create(messageData);
            
            // Update room activity
            room.totalMessages++;
            await room.updateActivity();
            
            // Update user activity
            this.user.messageCount++;
            await this.user.updateActivity();
            
            // Broadcast message to room users
            await this.broadcastToRoom(this.user.roomid, 'bc', message.toClientFormat());
            
            console.log(`💬 Message from ${this.user.topic} in ${room.topic}: ${msg.substring(0, 50)}...`);
        } catch (error) {
            console.error('❌ Broadcast message error:', error);
            this.sendError('Failed to send message');
        }
    }

    checkRateLimit() {
        const now = Date.now();
        const windowMs = 60000; // 1 minute
        const maxMessages = 30; // Max 30 messages per minute
        
        // Reset if window expired
        if (now - this.rateLimitData.lastReset > windowMs) {
            this.rateLimitData.messages = [];
            this.rateLimitData.lastReset = now;
        }
        
        // Add current message
        this.rateLimitData.messages.push(now);
        
        // Remove old messages
        this.rateLimitData.messages = this.rateLimitData.messages.filter(
            timestamp => now - timestamp < windowMs
        );
        
        return this.rateLimitData.messages.length <= maxMessages;
    }

    async broadcastToRoom(roomId, command, data) {
        try {
            const roomUserSet = this.roomUsers.get(roomId);
            if (!roomUserSet) return;
            
            const encryptedCommand = encryptCommand(command);
            
            for (const userId of roomUserSet) {
                const socketId = this.userSockets.get(userId);
                if (socketId) {
                    const socket = this.io.sockets.sockets.get(socketId);
                    if (socket) {
                        socket.send(JSON.stringify({
                            cmd: encryptedCommand,
                            data: data
                        }));
                    }
                }
            }
        } catch (error) {
            console.error('❌ Broadcast to room error:', error);
        }
    }

    sendEncrypted(command, data) {
        try {
            const encryptedCommand = encryptCommand(command);
            this.socket.send(JSON.stringify({
                cmd: encryptedCommand,
                data: data
            }));
        } catch (error) {
            console.error('❌ Send encrypted error:', error);
        }
    }

    sendError(message) {
        this.sendEncrypted('error', { message });
    }

    async handleJoinRoom(data) {
        try {
            if (!this.isAuthenticated || !this.user) {
                this.sendError('Not authenticated');
                return;
            }

            const { roomId, password } = data;

            // Find room
            const room = await Room.findOne({ id: roomId, isActive: true });
            if (!room) {
                this.sendError('Room not found');
                return;
            }

            // Check if room is full
            if (room.users >= room.maxUsers) {
                this.sendError('Room is full');
                return;
            }

            // Check password for private rooms
            if (room.isPrivate && room.password !== password) {
                this.sendError('Invalid room password');
                return;
            }

            // Check if user is banned
            if (room.isUserBanned(this.user.id)) {
                this.sendError('You are banned from this room');
                return;
            }

            // Leave current room if any
            if (this.user.roomid) {
                await this.handleLeaveRoom({ roomId: this.user.roomid });
            }

            // Join new room
            this.user.roomid = roomId;
            await this.user.save();

            // Update room user count
            await room.addUser();

            // Add to room users set
            if (!this.roomUsers.has(roomId)) {
                this.roomUsers.set(roomId, new Set());
            }
            this.roomUsers.get(roomId).add(this.user.id);

            // Send room data
            this.sendEncrypted('joined', {
                room: room.toClientFormat(),
                welcome: room.welcome
            });

            // Get recent messages
            const messages = await Message.findRoomMessages(roomId, 50);
            this.sendEncrypted('messages', messages.reverse().map(m => m.toClientFormat()));

            // Broadcast user join to room
            await this.broadcastToRoom(roomId, 'u+', this.user.toClientFormat());

            // Update room list for all users
            await this.broadcastRoomUpdate(room);

            console.log(`🚪 User ${this.user.topic} joined room ${room.topic}`);
        } catch (error) {
            console.error('❌ Join room error:', error);
            this.sendError('Failed to join room');
        }
    }

    async handleLeaveRoom(data) {
        try {
            if (!this.isAuthenticated || !this.user || !this.user.roomid) {
                return;
            }

            const roomId = this.user.roomid;

            // Find room
            const room = await Room.findOne({ id: roomId });
            if (room) {
                // Update room user count
                await room.removeUser();

                // Broadcast room update
                await this.broadcastRoomUpdate(room);
            }

            // Remove from room users set
            const roomUserSet = this.roomUsers.get(roomId);
            if (roomUserSet) {
                roomUserSet.delete(this.user.id);
                if (roomUserSet.size === 0) {
                    this.roomUsers.delete(roomId);
                }
            }

            // Broadcast user leave to room
            await this.broadcastToRoom(roomId, 'u-', this.user.id);

            // Update user
            this.user.roomid = null;
            await this.user.save();

            this.sendEncrypted('left', { roomId });

            console.log(`🚪 User ${this.user.topic} left room ${roomId}`);
        } catch (error) {
            console.error('❌ Leave room error:', error);
        }
    }

    async handleVoiceCommand(data) {
        try {
            if (!this.isAuthenticated || !this.user) {
                this.sendError('Not authenticated');
                return;
            }

            const { action, enabled } = data;

            switch (action) {
                case 'toggle':
                    this.user.micEnabled = enabled;
                    await this.user.save();

                    // Broadcast voice status to room
                    if (this.user.roomid) {
                        await this.broadcastToRoom(this.user.roomid, 'voice', {
                            userId: this.user.id,
                            enabled: enabled
                        });
                    }
                    break;

                case 'mute':
                    this.user.isMuted = enabled;
                    await this.user.save();
                    break;
            }

            this.sendEncrypted('voice_status', {
                micEnabled: this.user.micEnabled,
                isMuted: this.user.isMuted
            });
        } catch (error) {
            console.error('❌ Voice command error:', error);
            this.sendError('Voice command failed');
        }
    }

    async handleBinaryData(buffer) {
        try {
            if (!this.isAuthenticated || !this.user || !this.user.micEnabled) {
                return;
            }

            // Parse binary audio data
            const data = new Uint8Array(buffer);
            const type = data[0];

            if (type === 2) { // Voice data
                // Extract user ID and audio data
                const userIdLength = data[1];
                const userId = new TextDecoder().decode(data.slice(2, 2 + userIdLength));

                if (userId !== this.user.id) {
                    return; // Invalid user ID
                }

                const audioData = data.slice(2 + userIdLength);

                // Broadcast audio to room users
                if (this.user.roomid) {
                    await this.broadcastAudioToRoom(this.user.roomid, this.user.id, audioData);
                }
            }
        } catch (error) {
            console.error('❌ Binary data error:', error);
        }
    }

    async broadcastAudioToRoom(roomId, senderId, audioData) {
        try {
            const roomUserSet = this.roomUsers.get(roomId);
            if (!roomUserSet) return;

            // Create binary message
            const senderIdBuffer = new TextEncoder().encode(senderId);
            const messageBuffer = new Uint8Array(2 + senderIdBuffer.length + audioData.length);

            messageBuffer[0] = 2; // Audio type
            messageBuffer[1] = senderIdBuffer.length;
            messageBuffer.set(senderIdBuffer, 2);
            messageBuffer.set(audioData, 2 + senderIdBuffer.length);

            // Send to all room users except sender
            for (const userId of roomUserSet) {
                if (userId !== senderId) {
                    const socketId = this.userSockets.get(userId);
                    if (socketId) {
                        const socket = this.io.sockets.sockets.get(socketId);
                        if (socket) {
                            socket.emit('binary', messageBuffer);
                        }
                    }
                }
            }
        } catch (error) {
            console.error('❌ Broadcast audio error:', error);
        }
    }

    async broadcastRoomUpdate(room) {
        try {
            const roomData = room.toClientFormat();

            // Broadcast to all connected users
            for (const [socketId, user] of this.connectedUsers) {
                const socket = this.io.sockets.sockets.get(socketId);
                if (socket) {
                    socket.send(JSON.stringify({
                        cmd: encryptCommand('r^'),
                        data: roomData
                    }));
                }
            }
        } catch (error) {
            console.error('❌ Broadcast room update error:', error);
        }
    }

    async handleLogout(data) {
        try {
            if (this.user) {
                // Leave room if in one
                if (this.user.roomid) {
                    await this.handleLeaveRoom({});
                }

                // Update user status
                this.user.isOnline = false;
                this.user.socketId = null;
                await this.user.save();

                // Remove from memory
                this.connectedUsers.delete(this.socket.id);
                this.userSockets.delete(this.user.id);

                console.log(`👋 User ${this.user.topic} logged out`);
            }

            this.isAuthenticated = false;
            this.user = null;

            this.sendEncrypted('logout', { success: true });
        } catch (error) {
            console.error('❌ Logout error:', error);
        }
    }

    async handleDisconnect(reason) {
        try {
            if (this.user) {
                // Leave room if in one
                if (this.user.roomid) {
                    await this.handleLeaveRoom({});
                }

                // Update user status
                this.user.isOnline = false;
                this.user.socketId = null;
                await this.user.save();

                // Remove from memory
                this.connectedUsers.delete(this.socket.id);
                this.userSockets.delete(this.user.id);

                console.log(`🔌 User ${this.user.topic} disconnected: ${reason}`);
            }
        } catch (error) {
            console.error('❌ Disconnect error:', error);
        }
    }

    async handleAdminCommand(data) {
        try {
            // Check if user has admin privileges
            if (!this.user || !this.user.powers || !this.user.powers.includes('admin')) {
                this.sendError('Access denied');
                return;
            }

            // Set admin user in admin controller
            this.adminController.adminUser = this.user;
            this.adminController.isAdminAuthenticated = true;

            // Handle admin command
            await this.adminController.handleAdminCommand(data);
        } catch (error) {
            console.error('❌ Admin command error:', error);
            this.sendError('Admin command failed');
        }
    }

    async handleAdminMessage(data) {
        try {
            // Handle admin panel messages (CP interface)
            const [cpi, messageData] = data;

            if (!this.user || !this.user.powers || !this.user.powers.includes('admin')) {
                this.sendError('Access denied');
                return;
            }

            // Forward to admin controller
            this.adminController.adminUser = this.user;
            await this.adminController.handleAdminCommand(messageData);
        } catch (error) {
            console.error('❌ Admin message error:', error);
            this.sendError('Admin message failed');
        }
    }
}

module.exports = SocketController;
