const { v4: uuidv4 } = require('uuid');
const { encryptCommand, decryptCommand } = require('../utils/encryption');
const User = require('../models/User');
const Room = require('../models/Room');
const Message = require('../models/Message');
const AdminLog = require('../models/AdminLog');

class AdminController {
    constructor(socket, io, globalData) {
        this.socket = socket;
        this.io = io;
        this.connectedUsers = globalData.connectedUsers;
        this.userSockets = globalData.userSockets;
        this.roomUsers = globalData.roomUsers;
        
        this.adminUser = null;
        this.isAdminAuthenticated = false;
    }

    async handleAdminCommand(data) {
        try {
            const { cmd, q, data: commandData } = data;
            
            console.log(`🔧 Admin command: ${cmd} from ${this.socket.id}`);
            
            switch (cmd) {
                case 'powers':
                    await this.handleGetPowers();
                    break;
                case 'subs':
                    await this.handleGetSubscriptions();
                    break;
                case 'rooms':
                    await this.handleGetRooms();
                    break;
                case 'logins':
                    await this.handleGetLogins();
                    break;
                case 'bots':
                    await this.handleGetBots();
                    break;
                case 'bans':
                    await this.handleGetBans();
                    break;
                case 'domains':
                    await this.handleGetDomains();
                    break;
                case 'shrt':
                    await this.handleGetShortLinks();
                    break;
                case 'actions':
                    await this.handleGetActions();
                    break;
                case 'msgs':
                    await this.handleGetMessages();
                    break;
                case 'wrooms':
                    await this.handleGetWaitingRooms();
                    break;
                case 'stats':
                    await this.handleGetStats();
                    break;
                case 'owner':
                    await this.handleGetOwnerSettings();
                    break;
                case 'fps':
                    await this.handleGetFingerprints();
                    break;
                case 'fltr':
                    await this.handleGetFilters();
                    break;
                case 'sico':
                    await this.handleManageIcons(commandData);
                    break;
                default:
                    this.sendAdminResponse(cmd, { error: 'Unknown command' });
                    break;
            }
        } catch (error) {
            console.error('❌ Admin command error:', error);
            this.sendAdminResponse('error', { message: 'Command failed' });
        }
    }

    async handleGetPowers() {
        try {
            const users = await User.find({ 
                powers: { $exists: true, $not: { $size: 0 } } 
            }).select('id topic powers createdAt lastSeen');
            
            const powersData = users.map(user => ({
                id: user.id,
                topic: user.topic,
                powers: user.powers,
                createdAt: user.createdAt.getTime(),
                lastSeen: user.lastSeen.getTime()
            }));
            
            this.sendAdminResponse('powers', powersData);
        } catch (error) {
            console.error('❌ Get powers error:', error);
            this.sendAdminResponse('powers', { error: 'Failed to get powers' });
        }
    }

    async handleGetSubscriptions() {
        try {
            // Get premium/VIP users
            const subs = await User.find({ 
                powers: { $in: ['vip', 'premium'] } 
            }).select('id topic powers createdAt lastSeen rep');
            
            const subsData = subs.map(user => ({
                id: user.id,
                topic: user.topic,
                type: user.powers.includes('premium') ? 'premium' : 'vip',
                rep: user.rep,
                createdAt: user.createdAt.getTime(),
                lastSeen: user.lastSeen.getTime()
            }));
            
            this.sendAdminResponse('subs', subsData);
        } catch (error) {
            console.error('❌ Get subscriptions error:', error);
            this.sendAdminResponse('subs', { error: 'Failed to get subscriptions' });
        }
    }

    async handleGetRooms() {
        try {
            const rooms = await Room.find({}).sort({ createdAt: -1 }).limit(100);
            
            const roomsData = rooms.map(room => ({
                id: room.id,
                topic: room.topic,
                users: room.users,
                uco: room.uco,
                isActive: room.isActive,
                isPrivate: room.isPrivate,
                createdBy: room.createdBy,
                createdAt: room.createdAt.getTime(),
                totalMessages: room.totalMessages,
                category: room.category
            }));
            
            this.sendAdminResponse('rooms', roomsData);
        } catch (error) {
            console.error('❌ Get rooms error:', error);
            this.sendAdminResponse('rooms', { error: 'Failed to get rooms' });
        }
    }

    async handleGetLogins() {
        try {
            const logins = await AdminLog.find({ 
                action: { $in: ['login', 'logout'] } 
            }).sort({ createdAt: -1 }).limit(200);
            
            const loginsData = logins.map(log => ({
                id: log.id,
                action: log.action,
                adminTopic: log.adminTopic,
                adminIp: log.adminIp,
                createdAt: log.createdAt.getTime(),
                userAgent: log.userAgent,
                status: log.status
            }));
            
            this.sendAdminResponse('logins', loginsData);
        } catch (error) {
            console.error('❌ Get logins error:', error);
            this.sendAdminResponse('logins', { error: 'Failed to get logins' });
        }
    }

    async handleGetBots() {
        try {
            // Get users that might be bots (based on patterns)
            const bots = await User.find({
                $or: [
                    { topic: /^bot_/i },
                    { topic: /^زائر_\d{4}$/ },
                    { messageCount: { $gt: 1000 } }
                ]
            }).select('id topic messageCount createdAt lastSeen fingerprint');
            
            const botsData = bots.map(bot => ({
                id: bot.id,
                topic: bot.topic,
                messageCount: bot.messageCount,
                createdAt: bot.createdAt.getTime(),
                lastSeen: bot.lastSeen.getTime(),
                hasFingerprint: !!bot.fingerprint
            }));
            
            this.sendAdminResponse('bots', botsData);
        } catch (error) {
            console.error('❌ Get bots error:', error);
            this.sendAdminResponse('bots', { error: 'Failed to get bots' });
        }
    }

    async handleGetBans() {
        try {
            const bannedUsers = await User.find({ 
                isBanned: true 
            }).select('id topic banReason banExpires createdAt');
            
            const bansData = bannedUsers.map(user => ({
                id: user.id,
                topic: user.topic,
                reason: user.banReason,
                expires: user.banExpires ? user.banExpires.getTime() : null,
                createdAt: user.createdAt.getTime()
            }));
            
            this.sendAdminResponse('bans', bansData);
        } catch (error) {
            console.error('❌ Get bans error:', error);
            this.sendAdminResponse('bans', { error: 'Failed to get bans' });
        }
    }

    async handleGetDomains() {
        try {
            // Get unique domains from user IPs (would need IP logging)
            const domains = [
                { domain: 'localhost', count: 10, blocked: false },
                { domain: '***********/24', count: 5, blocked: false }
            ];
            
            this.sendAdminResponse('domains', domains);
        } catch (error) {
            console.error('❌ Get domains error:', error);
            this.sendAdminResponse('domains', { error: 'Failed to get domains' });
        }
    }

    async handleGetShortLinks() {
        try {
            // Get messages with links
            const messagesWithLinks = await Message.find({ 
                link: { $exists: true, $ne: null } 
            }).select('id link userId userTopic createdAt').sort({ createdAt: -1 }).limit(100);
            
            const linksData = messagesWithLinks.map(msg => ({
                id: msg.id,
                link: msg.link,
                userId: msg.userId,
                userTopic: msg.userTopic,
                createdAt: msg.createdAt.getTime()
            }));
            
            this.sendAdminResponse('shrt', linksData);
        } catch (error) {
            console.error('❌ Get short links error:', error);
            this.sendAdminResponse('shrt', { error: 'Failed to get short links' });
        }
    }

    async handleGetActions() {
        try {
            const actions = await AdminLog.findRecentActions(24, 100);
            
            const actionsData = actions.map(action => action.toClientFormat());
            
            this.sendAdminResponse('actions', actionsData);
        } catch (error) {
            console.error('❌ Get actions error:', error);
            this.sendAdminResponse('actions', { error: 'Failed to get actions' });
        }
    }

    async handleGetMessages() {
        try {
            const messages = await Message.find({})
                .sort({ createdAt: -1 })
                .limit(200)
                .select('id msg userId userTopic roomId createdAt isDeleted');
            
            const messagesData = messages.map(msg => ({
                id: msg.id,
                msg: msg.isDeleted ? '[محذوفة]' : msg.msg.substring(0, 100),
                userId: msg.userId,
                userTopic: msg.userTopic,
                roomId: msg.roomId,
                createdAt: msg.createdAt.getTime(),
                isDeleted: msg.isDeleted
            }));
            
            this.sendAdminResponse('msgs', messagesData);
        } catch (error) {
            console.error('❌ Get messages error:', error);
            this.sendAdminResponse('msgs', { error: 'Failed to get messages' });
        }
    }

    async handleGetWaitingRooms() {
        try {
            const waitingRooms = await Room.find({ 
                isActive: false 
            }).sort({ createdAt: -1 }).limit(50);
            
            const roomsData = waitingRooms.map(room => room.toClientFormat());
            
            this.sendAdminResponse('wrooms', roomsData);
        } catch (error) {
            console.error('❌ Get waiting rooms error:', error);
            this.sendAdminResponse('wrooms', { error: 'Failed to get waiting rooms' });
        }
    }

    async handleGetStats() {
        try {
            const stats = {
                users: {
                    total: await User.countDocuments(),
                    online: await User.countDocuments({ isOnline: true }),
                    banned: await User.countDocuments({ isBanned: true }),
                    today: await User.countDocuments({
                        createdAt: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) }
                    })
                },
                rooms: {
                    total: await Room.countDocuments(),
                    active: await Room.countDocuments({ isActive: true }),
                    private: await Room.countDocuments({ isPrivate: true })
                },
                messages: {
                    total: await Message.countDocuments(),
                    today: await Message.countDocuments({
                        createdAt: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) }
                    }),
                    deleted: await Message.countDocuments({ isDeleted: true })
                },
                server: {
                    uptime: process.uptime(),
                    memory: process.memoryUsage(),
                    connections: this.io.engine.clientsCount
                }
            };
            
            this.sendAdminResponse('stats', stats);
        } catch (error) {
            console.error('❌ Get stats error:', error);
            this.sendAdminResponse('stats', { error: 'Failed to get stats' });
        }
    }

    async handleGetOwnerSettings() {
        try {
            const settings = {
                maxRooms: process.env.MAX_ROOMS || 100,
                maxUsersPerRoom: process.env.MAX_USERS_PER_ROOM || 50,
                messageRetentionDays: process.env.OLD_MESSAGE_CLEANUP_DAYS || 30,
                inactiveUserTimeout: process.env.INACTIVE_USER_TIMEOUT_MINUTES || 60,
                rateLimit: {
                    windowMs: process.env.RATE_LIMIT_WINDOW_MS || 900000,
                    maxRequests: process.env.RATE_LIMIT_MAX_REQUESTS || 100
                }
            };
            
            this.sendAdminResponse('owner', settings);
        } catch (error) {
            console.error('❌ Get owner settings error:', error);
            this.sendAdminResponse('owner', { error: 'Failed to get settings' });
        }
    }

    async handleGetFingerprints() {
        try {
            const fingerprints = await User.aggregate([
                { $match: { fingerprint: { $exists: true, $ne: null } } },
                { $group: { _id: '$fingerprint', count: { $sum: 1 }, users: { $push: { id: '$id', topic: '$topic' } } } },
                { $match: { count: { $gt: 1 } } },
                { $sort: { count: -1 } },
                { $limit: 50 }
            ]);
            
            this.sendAdminResponse('fps', fingerprints);
        } catch (error) {
            console.error('❌ Get fingerprints error:', error);
            this.sendAdminResponse('fps', { error: 'Failed to get fingerprints' });
        }
    }

    async handleGetFilters() {
        try {
            const filters = {
                badWords: ['spam', 'scam', 'hack'],
                blockedDomains: ['malicious.com'],
                autoMod: {
                    enabled: true,
                    maxMessageLength: 500,
                    slowMode: false
                }
            };
            
            this.sendAdminResponse('fltr', filters);
        } catch (error) {
            console.error('❌ Get filters error:', error);
            this.sendAdminResponse('fltr', { error: 'Failed to get filters' });
        }
    }

    async handleManageIcons(data) {
        try {
            // Handle icon management
            const icons = []; // Would load from database or file system
            
            this.sendAdminResponse('sico', icons);
        } catch (error) {
            console.error('❌ Manage icons error:', error);
            this.sendAdminResponse('sico', { error: 'Failed to manage icons' });
        }
    }

    sendAdminResponse(command, data) {
        try {
            const encryptedCommand = encryptCommand(command);
            this.socket.send(JSON.stringify({
                cmd: encryptedCommand,
                data: data
            }));
        } catch (error) {
            console.error('❌ Send admin response error:', error);
        }
    }

    async logAdminAction(action, details = {}) {
        try {
            if (!this.adminUser) return;
            
            await AdminLog.logAction({
                action: action,
                adminId: this.adminUser.id,
                adminTopic: this.adminUser.topic,
                adminIp: this.socket.handshake.address,
                details: details,
                userAgent: this.socket.handshake.headers['user-agent'],
                severity: 'medium'
            });
        } catch (error) {
            console.error('❌ Log admin action error:', error);
        }
    }
}

module.exports = AdminController;
