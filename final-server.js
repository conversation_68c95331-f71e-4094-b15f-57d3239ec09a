const express = require('express');
const http = require('http');
const WebSocket = require('ws');
const path = require('path');
const url = require('url');

console.log('🚀 Starting FINAL chat server...');

const app = express();
const server = http.createServer(app);

// Basic middleware
app.use(express.static(path.join(__dirname, 'public')));
app.use(express.json());

// Simple franzetta encryption function
function franzetta(text) {
    if (!text || typeof text !== 'string') return '';
    let result = '';
    for (let i = 0; i < text.length; i++) {
        result += String.fromCharCode(text.charCodeAt(i) ^ ((i + 1) % 8));
    }
    return result;
}

function encryptCommand(command) {
    return franzetta(command);
}

function decryptCommand(encryptedCommand) {
    return franzetta(encryptedCommand);
}

// Simple in-memory storage
const connectedUsers = new Map();
const rooms = new Map();

// Initialize default rooms
rooms.set('general', {
    id: 'general',
    topic: 'الغرفة العامة',
    users: 0,
    uco: 0,
    lupd: Date.now(),
    isActive: true,
    pic: 'room.webp',
    owner: 'admin',
    needpass: false,
    v: false,
    l: false,
    nos: false,
    b: false,
    wroom: false,
    c: '#000000',
    h: '#01'
});

rooms.set('welcome', {
    id: 'welcome',
    topic: 'غرفة الترحيب',
    users: 0,
    uco: 0,
    lupd: Date.now(),
    isActive: true,
    pic: 'room.webp',
    owner: 'admin',
    needpass: false,
    v: false,
    l: false,
    nos: false,
    b: false,
    wroom: false,
    c: '#000000',
    h: '#02'
});

// API Routes
app.get('/api/health', (req, res) => {
    res.json({
        status: 'ok',
        connections: wss.clients.size,
        users: connectedUsers.size,
        rooms: rooms.size
    });
});

app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// WebSocket Server
const wss = new WebSocket.Server({
    server: server,
    path: '/socket.io/',
    verifyClient: (info) => {
        const query = url.parse(info.req.url, true).query;
        return query.EIO === '4' && query.transport === 'websocket';
    }
});

// WebSocket connection handling
wss.on('connection', (ws, req) => {
    console.log(`🔌 NEW CONNECTION (Total: ${wss.clients.size})`);
    
    let user = null;
    let isAuthenticated = false;
    let connectionId = `ws_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // Send initial hi message directly (no Socket.IO protocol)
    setTimeout(() => {
        const hiMsg = { cmd: 'hi', data: 'server_hello_' + Date.now() };
        ws.send(JSON.stringify(hiMsg));
        console.log('📤 Sent hi message');
    }, 500);
    
    // Handle messages
    ws.on('message', (data) => {
        try {
            const message = data.toString();
            console.log(`📨 Received: ${message.substring(0, 100)}...`);
            
            // Only handle JSON messages
            if (!message.startsWith('{')) {
                console.log(`❓ Ignoring non-JSON message: ${message}`);
                return;
            }

            const parsed = JSON.parse(message);
            
            const { cmd, data: messageData } = parsed;
            const decryptedCmd = decryptCommand(cmd);
            console.log(`🎯 Command: ${decryptedCmd}`);
            
            switch (decryptedCmd) {
                case 'hi':
                    console.log('🤝 Processing handshake...');
                    const response = franzetta(messageData || 'hello');
                    const responseMsg = { cmd: encryptCommand('hi'), data: response };
                    ws.send(JSON.stringify(responseMsg));
                    console.log('✅ Handshake completed');
                    break;
                    
                case 'ping':
                    const pongMsg = { cmd: encryptCommand('pong'), data: messageData };
                    ws.send(JSON.stringify(pongMsg));
                    console.log('🏓 Pong sent');
                    break;
                    
                case 'online':
                    console.log('👤 Processing login...');
                    const userId = `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
                    user = {
                        id: userId,
                        topic: `زائر_${Math.floor(Math.random() * 9999)}`,
                        rep: 0,
                        pic: 'pic.webp',
                        msg: '',
                        ico: '',
                        roomid: null,
                        v: 0,
                        bg: '#ffffff',
                        ucol: '#000000',
                        s: false,
                        co: '--',
                        lupd: Date.now(),
                        socketId: connectionId,
                        isOnline: true,
                        power: 0,
                        lid: userId,
                        nick: `زائر_${Math.floor(Math.random() * 9999)}`,
                        avatar: 'pic.webp',
                        rank: 0
                    };
                    
                    connectedUsers.set(connectionId, user);
                    isAuthenticated = true;
                    
                    // Send success response
                    const okMsg = { cmd: encryptCommand('ok'), data: { k: 'demo_token_' + userId } };
                    ws.send(JSON.stringify(okMsg));
                    console.log(`✅ User ${user.topic} logged in`);
                    
                    // Send initial data
                    setTimeout(() => {
                        // Send user list
                        const userList = Array.from(connectedUsers.values());
                        const ulistMsg = { cmd: encryptCommand('ulist'), data: userList };
                        ws.send(JSON.stringify(ulistMsg));
                        
                        // Send room list
                        const roomList = Array.from(rooms.values());
                        const rlistMsg = { cmd: encryptCommand('rlist'), data: roomList };
                        ws.send(JSON.stringify(rlistMsg));
                        
                        // Send empty data
                        ws.send(JSON.stringify({ cmd: encryptCommand('emos'), data: [] }));
                        ws.send(JSON.stringify({ cmd: encryptCommand('dro3'), data: [] }));
                        
                        console.log('📋 Initial data sent');
                    }, 1000);
                    break;
                    
                case 'join':
                    if (!isAuthenticated || !user) return;
                    
                    const { roomId } = messageData;
                    const room = rooms.get(roomId);
                    if (!room) return;
                    
                    user.roomid = roomId;
                    room.users++;
                    room.uco++;
                    
                    const joinedMsg = {
                        cmd: encryptCommand('joined'),
                        data: { room: room, welcome: `مرحباً بك في ${room.topic}` }
                    };
                    ws.send(JSON.stringify(joinedMsg));
                    
                    // Send empty messages
                    ws.send(JSON.stringify({ cmd: encryptCommand('messages'), data: [] }));
                    
                    console.log(`🚪 User joined room ${room.topic}`);
                    break;
                    
                case 'bc':
                    if (!isAuthenticated || !user || !user.roomid) return;
                    
                    const { msg } = messageData;
                    if (!msg || msg.trim() === '') return;
                    
                    const messageId = `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
                    const message = {
                        id: messageId,
                        msg: msg.trim(),
                        userId: user.id,
                        userTopic: user.topic,
                        userPic: user.pic,
                        userBg: user.bg,
                        userColor: user.ucol,
                        userIco: user.ico,
                        roomId: user.roomid,
                        createdAt: Date.now()
                    };
                    
                    // Broadcast to all users in room
                    for (const [socketId, connectedUser] of connectedUsers) {
                        if (connectedUser.roomid === user.roomid) {
                            wss.clients.forEach(client => {
                                if (client.readyState === WebSocket.OPEN) {
                                    client.send(JSON.stringify({
                                        cmd: encryptCommand('bc'),
                                        data: message
                                    }));
                                }
                            });
                        }
                    }
                    
                    console.log(`💬 Message: ${msg.substring(0, 30)}...`);
                    break;
                    
                default:
                    console.log(`❓ Unknown command: ${decryptedCmd}`);
                    break;
            }
        } catch (error) {
            console.error('❌ Error:', error.message);
        }
    });
    
    // Handle disconnection
    ws.on('close', (code, reason) => {
        if (user) {
            connectedUsers.delete(connectionId);
            console.log(`👋 User ${user.topic} disconnected`);
        }
        console.log(`🔌 Disconnected (Remaining: ${wss.clients.size})`);
    });
    
    // Handle errors
    ws.on('error', (error) => {
        console.error(`❌ WebSocket error: ${error.message}`);
    });
});

// Start server
const PORT = 3004;
server.listen(PORT, () => {
    console.log(`🚀 Server running on http://localhost:${PORT}`);
});

console.log('✅ Server ready!');
