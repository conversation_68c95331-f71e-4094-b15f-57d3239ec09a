const mongoose = require('mongoose');

const userSchema = new mongoose.Schema({
    // Basic user information
    id: {
        type: String,
        required: true,
        unique: true,
        index: true
    },
    topic: {
        type: String,
        required: true,
        maxlength: 50,
        trim: true
    },
    rep: {
        type: Number,
        default: 0,
        min: -1000,
        max: 10000
    },
    pic: {
        type: String,
        default: 'pic.webp'
    },
    msg: {
        type: String,
        maxlength: 200,
        default: ''
    },
    ico: {
        type: String,
        default: ''
    },
    roomid: {
        type: String,
        default: null,
        index: true
    },
    v: {
        type: Number,
        default: 0 // Voice/activity level
    },
    bg: {
        type: String,
        default: '#ffffff'
    },
    ucol: {
        type: String,
        default: '#000000'
    },
    
    // Authentication
    token: {
        type: String,
        unique: true,
        sparse: true
    },
    password: {
        type: String,
        select: false
    },
    
    // Connection info
    socketId: {
        type: String,
        default: null
    },
    isOnline: {
        type: Boolean,
        default: false,
        index: true
    },
    lastSeen: {
        type: Date,
        default: Date.now,
        index: true
    },
    
    // User status
    s: {
        type: Boolean,
        default: false // Hidden/shadow user
    },
    co: {
        type: String,
        default: '--' // Country code
    },
    
    // Permissions and roles
    lid: {
        type: String,
        default: null // Login ID for admin purposes
    },
    powers: [{
        type: String,
        enum: ['admin', 'moderator', 'vip', 'ban', 'kick', 'mute']
    }],
    
    // Activity tracking
    lupd: {
        type: Date,
        default: Date.now,
        index: true
    },
    joinTime: {
        type: Date,
        default: Date.now
    },
    
    // Voice chat
    micEnabled: {
        type: Boolean,
        default: false
    },
    isMuted: {
        type: Boolean,
        default: false
    },
    
    // Fingerprint for security
    fingerprint: {
        type: String,
        default: null
    },
    
    // Ban information
    isBanned: {
        type: Boolean,
        default: false,
        index: true
    },
    banReason: {
        type: String,
        default: null
    },
    banExpires: {
        type: Date,
        default: null
    },
    
    // Statistics
    messageCount: {
        type: Number,
        default: 0
    },
    voiceMinutes: {
        type: Number,
        default: 0
    }
}, {
    timestamps: true,
    toJSON: {
        transform: function(doc, ret) {
            delete ret._id;
            delete ret.__v;
            delete ret.password;
            delete ret.token;
            delete ret.socketId;
            return ret;
        }
    }
});

// Indexes for performance
userSchema.index({ isOnline: 1, roomid: 1 });
userSchema.index({ lupd: -1 });
userSchema.index({ lastSeen: -1 });
userSchema.index({ 'powers': 1 });

// Methods
userSchema.methods.toClientFormat = function() {
    return {
        id: this.id,
        topic: this.topic,
        rep: this.rep,
        pic: this.pic,
        msg: this.msg,
        ico: this.ico,
        roomid: this.roomid,
        v: this.v,
        bg: this.bg,
        ucol: this.ucol,
        s: this.s,
        co: this.co,
        lupd: this.lupd.getTime()
    };
};

userSchema.methods.updateActivity = function() {
    this.lupd = new Date();
    this.lastSeen = new Date();
    return this.save();
};

// Static methods
userSchema.statics.findOnlineUsers = function(roomId = null) {
    const query = { isOnline: true, isBanned: false };
    if (roomId) {
        query.roomid = roomId;
    }
    return this.find(query).sort({ v: -1, lupd: -1 });
};

userSchema.statics.cleanupInactiveUsers = function(timeoutMinutes = 60) {
    const cutoffTime = new Date(Date.now() - timeoutMinutes * 60 * 1000);
    return this.updateMany(
        { lastSeen: { $lt: cutoffTime }, isOnline: true },
        { $set: { isOnline: false, socketId: null, roomid: null } }
    );
};

module.exports = mongoose.model('User', userSchema);
