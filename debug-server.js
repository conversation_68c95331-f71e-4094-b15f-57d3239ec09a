const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const path = require('path');

console.log('🚀 Starting DEBUG chat server...');
console.log('📅 Time:', new Date().toISOString());

const app = express();
const server = http.createServer(app);

// Socket.IO configuration with detailed logging
const io = socketIo(server, {
    cors: {
        origin: "*",
        methods: ["GET", "POST"],
        credentials: true
    },
    transports: ['websocket', 'polling'], // Allow both transports
    pingTimeout: 30000,
    pingInterval: 10000,
    allowEIO3: true,
    allowEIO4: true,
    allowUpgrades: true,
    cookie: false
});

console.log('✅ Socket.IO configured with transports: websocket, polling');

// Basic middleware
app.use(express.static(path.join(__dirname, 'public')));
app.use(express.json());

// Add request logging
app.use((req, res, next) => {
    console.log(`📡 HTTP ${req.method} ${req.url} from ${req.ip}`);
    next();
});

console.log('✅ Middleware configured');

// Simple franzetta encryption function
function franzetta(text) {
    if (!text || typeof text !== 'string') {
        return '';
    }
    
    let result = '';
    for (let i = 0; i < text.length; i++) {
        result += String.fromCharCode(text.charCodeAt(i) ^ ((i + 1) % 8));
    }
    
    return result;
}

function encryptCommand(command) {
    const encrypted = franzetta(command);
    console.log(`🔐 Encrypting: "${command}" -> "${encrypted}"`);
    return encrypted;
}

function decryptCommand(encryptedCommand) {
    const decrypted = franzetta(encryptedCommand);
    console.log(`🔓 Decrypting: "${encryptedCommand}" -> "${decrypted}"`);
    return decrypted;
}

console.log('✅ Encryption functions ready');

// Simple in-memory storage
const connectedUsers = new Map();
const userSockets = new Map();
const rooms = new Map();

// Initialize default rooms
rooms.set('general', {
    id: 'general',
    topic: 'الغرفة العامة',
    users: 0,
    uco: 0,
    lupd: Date.now(),
    isActive: true
});

rooms.set('welcome', {
    id: 'welcome',
    topic: 'غرفة الترحيب',
    users: 0,
    uco: 0,
    lupd: Date.now(),
    isActive: true
});

console.log('✅ Default rooms created:', Array.from(rooms.keys()));

// API Routes
app.get('/api/health', (req, res) => {
    const health = {
        status: 'ok',
        timestamp: new Date().toISOString(),
        connections: io.engine.clientsCount,
        users: connectedUsers.size,
        rooms: rooms.size,
        uptime: process.uptime()
    };
    console.log('🏥 Health check requested:', health);
    res.json(health);
});

app.get('/api/debug', (req, res) => {
    const debug = {
        connectedUsers: Array.from(connectedUsers.values()),
        rooms: Array.from(rooms.values()),
        socketCount: io.engine.clientsCount
    };
    console.log('🐛 Debug info requested');
    res.json(debug);
});

app.get('/', (req, res) => {
    console.log('🏠 Serving main page');
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

app.get('/cp', (req, res) => {
    console.log('🛠️ Serving admin panel');
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

console.log('✅ Routes configured');

// Socket.IO connection handling with detailed logging
io.on('connection', (socket) => {
    console.log(`🔌 NEW CONNECTION: ${socket.id}`);
    console.log(`📊 Total connections: ${io.engine.clientsCount}`);
    console.log(`🌐 Client info:`, {
        transport: socket.conn.transport.name,
        upgraded: socket.conn.upgraded,
        remoteAddress: socket.conn.remoteAddress,
        userAgent: socket.handshake.headers['user-agent']
    });
    
    let user = null;
    let isAuthenticated = false;
    
    // Handle messages with detailed logging
    socket.on('message', (data) => {
        console.log(`📨 RAW MESSAGE from ${socket.id}:`, typeof data, data);
        
        try {
            if (typeof data === 'string') {
                console.log(`📝 Parsing JSON message...`);
                const parsed = JSON.parse(data);
                console.log(`📋 Parsed message:`, parsed);
                
                const { cmd, data: messageData } = parsed;
                console.log(`🎯 Command received: "${cmd}"`);
                
                // Decrypt command
                const decryptedCmd = decryptCommand(cmd);
                console.log(`🔓 Decrypted command: "${decryptedCmd}"`);
                
                switch (decryptedCmd) {
                    case 'hi':
                        console.log('🤝 Processing handshake...');
                        const response = franzetta(messageData || 'hello');
                        const responseMsg = {
                            cmd: encryptCommand('hi'),
                            data: response
                        };
                        console.log('📤 Sending handshake response:', responseMsg);
                        socket.send(JSON.stringify(responseMsg));
                        console.log('✅ Handshake completed');
                        break;
                        
                    case 'ping':
                        console.log('🏓 Processing ping...');
                        const pongMsg = {
                            cmd: encryptCommand('pong'),
                            data: messageData
                        };
                        console.log('📤 Sending pong:', pongMsg);
                        socket.send(JSON.stringify(pongMsg));
                        console.log('✅ Pong sent');
                        break;
                        
                    case 'online':
                        console.log('👤 Processing user login...');
                        console.log('📋 Login data:', messageData);
                        
                        const userId = `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
                        user = {
                            id: userId,
                            topic: `زائر_${Math.floor(Math.random() * 9999)}`,
                            rep: 0,
                            pic: 'pic.webp',
                            msg: '',
                            ico: '',
                            roomid: null,
                            v: 0,
                            bg: '#ffffff',
                            ucol: '#000000',
                            s: false,
                            co: '--',
                            lupd: Date.now(),
                            socketId: socket.id,
                            isOnline: true
                        };
                        
                        connectedUsers.set(socket.id, user);
                        userSockets.set(userId, socket.id);
                        isAuthenticated = true;
                        
                        console.log(`👤 User created:`, user);
                        
                        // Send success response
                        const okMsg = {
                            cmd: encryptCommand('ok'),
                            data: { k: 'demo_token_' + userId }
                        };
                        console.log('📤 Sending OK response:', okMsg);
                        socket.send(JSON.stringify(okMsg));
                        
                        console.log(`✅ User ${user.topic} logged in successfully`);
                        
                        // Send initial data after a delay
                        setTimeout(() => {
                            try {
                                console.log('📋 Sending initial data...');
                                
                                // Send user list
                                const userList = Array.from(connectedUsers.values());
                                const ulistMsg = {
                                    cmd: encryptCommand('ulist'),
                                    data: userList
                                };
                                console.log('📤 Sending user list:', ulistMsg);
                                socket.send(JSON.stringify(ulistMsg));
                                
                                // Send room list
                                const roomList = Array.from(rooms.values());
                                const rlistMsg = {
                                    cmd: encryptCommand('rlist'),
                                    data: roomList
                                };
                                console.log('📤 Sending room list:', rlistMsg);
                                socket.send(JSON.stringify(rlistMsg));
                                
                                // Send empty arrays for other data
                                const emosMsg = {
                                    cmd: encryptCommand('emos'),
                                    data: []
                                };
                                socket.send(JSON.stringify(emosMsg));
                                
                                const dro3Msg = {
                                    cmd: encryptCommand('dro3'),
                                    data: []
                                };
                                socket.send(JSON.stringify(dro3Msg));
                                
                                console.log('✅ Initial data sent successfully');
                            } catch (error) {
                                console.error('❌ Error sending initial data:', error);
                            }
                        }, 1000);
                        break;
                        
                    case 'join':
                        console.log('🚪 Processing room join...');
                        if (!isAuthenticated || !user) {
                            console.log('❌ User not authenticated for room join');
                            return;
                        }
                        
                        const { roomId } = messageData;
                        console.log(`🚪 User wants to join room: ${roomId}`);
                        
                        const room = rooms.get(roomId);
                        if (!room) {
                            console.log(`❌ Room not found: ${roomId}`);
                            return;
                        }
                        
                        user.roomid = roomId;
                        room.users++;
                        room.uco++;
                        
                        const joinedMsg = {
                            cmd: encryptCommand('joined'),
                            data: {
                                room: room,
                                welcome: `مرحباً بك في ${room.topic}`
                            }
                        };
                        socket.send(JSON.stringify(joinedMsg));
                        
                        console.log(`✅ User ${user.topic} joined room ${room.topic}`);
                        break;
                        
                    default:
                        console.log(`❓ Unknown command: "${decryptedCmd}"`);
                        break;
                }
            } else {
                console.log('❌ Message is not a string:', typeof data);
            }
        } catch (error) {
            console.error('❌ Error handling message:', error);
            console.error('❌ Stack trace:', error.stack);
        }
    });
    
    // Handle disconnection
    socket.on('disconnect', (reason) => {
        console.log(`🔌 DISCONNECTION: ${socket.id} - Reason: ${reason}`);
        
        if (user) {
            connectedUsers.delete(socket.id);
            userSockets.delete(user.id);
            console.log(`👋 User ${user.topic} disconnected`);
        }
        
        console.log(`📊 Remaining connections: ${io.engine.clientsCount}`);
    });
    
    // Handle errors
    socket.on('error', (error) => {
        console.error(`❌ SOCKET ERROR for ${socket.id}:`, error);
    });
    
    // Handle connect_error
    socket.on('connect_error', (error) => {
        console.error(`❌ CONNECTION ERROR for ${socket.id}:`, error);
    });
});

console.log('✅ Socket handlers configured');

// Start server
const PORT = process.env.PORT || 3001;
server.listen(PORT, () => {
    console.log(`🚀 DEBUG chat server running on port ${PORT}`);
    console.log(`🌐 Open http://localhost:${PORT} in your browser`);
    console.log(`📊 Health check: http://localhost:${PORT}/api/health`);
    console.log(`🐛 Debug info: http://localhost:${PORT}/api/debug`);
    console.log('⏰ Server started at:', new Date().toISOString());
});

// Log any unhandled errors
process.on('uncaughtException', (error) => {
    console.error('💥 UNCAUGHT EXCEPTION:', error);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('💥 UNHANDLED REJECTION:', reason);
});

console.log('✅ Server setup complete - Ready for connections!');
