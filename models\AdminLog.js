const mongoose = require('mongoose');

const adminLogSchema = new mongoose.Schema({
    // Log identification
    id: {
        type: String,
        required: true,
        unique: true,
        index: true
    },
    
    // Action details
    action: {
        type: String,
        required: true,
        enum: [
            'login', 'logout', 'ban', 'unban', 'kick', 'mute', 'unmute',
            'delete_message', 'edit_room', 'create_room', 'delete_room',
            'add_power', 'remove_power', 'change_settings', 'view_logs',
            'upload_file', 'delete_file', 'system_command'
        ],
        index: true
    },
    
    // Admin information
    adminId: {
        type: String,
        required: true,
        index: true
    },
    adminTopic: {
        type: String,
        required: true
    },
    adminIp: {
        type: String,
        required: true
    },
    
    // Target information (if applicable)
    targetId: {
        type: String,
        default: null,
        index: true
    },
    targetTopic: {
        type: String,
        default: null
    },
    targetType: {
        type: String,
        enum: ['user', 'room', 'message', 'file', 'system'],
        default: null
    },
    
    // Action details
    details: {
        type: mongoose.Schema.Types.Mixed,
        default: {}
    },
    reason: {
        type: String,
        maxlength: 500,
        default: null
    },
    
    // Context information
    roomId: {
        type: String,
        default: null,
        index: true
    },
    messageId: {
        type: String,
        default: null
    },
    
    // System information
    userAgent: {
        type: String,
        default: null
    },
    fingerprint: {
        type: String,
        default: null
    },
    
    // Status
    status: {
        type: String,
        enum: ['success', 'failed', 'pending'],
        default: 'success',
        index: true
    },
    errorMessage: {
        type: String,
        default: null
    },
    
    // Duration (for timed actions like bans)
    duration: {
        type: Number, // in minutes
        default: null
    },
    expiresAt: {
        type: Date,
        default: null,
        index: true
    },
    
    // Severity level
    severity: {
        type: String,
        enum: ['low', 'medium', 'high', 'critical'],
        default: 'medium',
        index: true
    },
    
    // Additional metadata
    metadata: {
        sessionId: String,
        requestId: String,
        version: String,
        platform: String
    }
}, {
    timestamps: true,
    toJSON: {
        transform: function(doc, ret) {
            delete ret._id;
            delete ret.__v;
            return ret;
        }
    }
});

// Indexes for performance
adminLogSchema.index({ createdAt: -1 });
adminLogSchema.index({ adminId: 1, createdAt: -1 });
adminLogSchema.index({ action: 1, createdAt: -1 });
adminLogSchema.index({ severity: 1, createdAt: -1 });
adminLogSchema.index({ expiresAt: 1 }, { sparse: true });

// Methods
adminLogSchema.methods.toClientFormat = function() {
    return {
        id: this.id,
        action: this.action,
        adminTopic: this.adminTopic,
        targetTopic: this.targetTopic,
        targetType: this.targetType,
        reason: this.reason,
        roomId: this.roomId,
        status: this.status,
        severity: this.severity,
        duration: this.duration,
        createdAt: this.createdAt.getTime(),
        details: this.details
    };
};

// Static methods
adminLogSchema.statics.logAction = function(actionData) {
    const log = new this({
        id: require('uuid').v4(),
        ...actionData,
        createdAt: new Date()
    });
    
    return log.save();
};

adminLogSchema.statics.findAdminActions = function(adminId, limit = 100) {
    return this.find({ adminId })
        .sort({ createdAt: -1 })
        .limit(limit);
};

adminLogSchema.statics.findActionsByType = function(action, limit = 100) {
    return this.find({ action })
        .sort({ createdAt: -1 })
        .limit(limit);
};

adminLogSchema.statics.findRecentActions = function(hours = 24, limit = 200) {
    const since = new Date(Date.now() - hours * 60 * 60 * 1000);
    return this.find({ createdAt: { $gte: since } })
        .sort({ createdAt: -1 })
        .limit(limit);
};

adminLogSchema.statics.findCriticalActions = function(limit = 50) {
    return this.find({ severity: 'critical' })
        .sort({ createdAt: -1 })
        .limit(limit);
};

adminLogSchema.statics.cleanupOldLogs = function(days = 90) {
    const cutoffDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000);
    return this.deleteMany({
        createdAt: { $lt: cutoffDate },
        severity: { $nin: ['high', 'critical'] }
    });
};

adminLogSchema.statics.getActionStats = function(days = 7) {
    const since = new Date(Date.now() - days * 24 * 60 * 60 * 1000);
    
    return this.aggregate([
        { $match: { createdAt: { $gte: since } } },
        {
            $group: {
                _id: '$action',
                count: { $sum: 1 },
                lastAction: { $max: '$createdAt' }
            }
        },
        { $sort: { count: -1 } }
    ]);
};

module.exports = mongoose.model('AdminLog', adminLogSchema);
