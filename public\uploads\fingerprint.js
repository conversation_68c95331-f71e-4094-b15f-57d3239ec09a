/**
 * Device Fingerprinting Module
 * 
 * This module collects device information to create a unique fingerprint
 * that is difficult to spoof, unlike user agent strings.
 */

(function() {
    // Store the fingerprint data
    let fingerprintData = {};
    
    // Collect basic device information
    function collectBasicInfo() {
        const nav = window.navigator;
        
        fingerprintData = {
            userAgent: nav.userAgent,
            language: nav.language,
            platform: nav.platform,
            cookiesEnabled: nav.cookieEnabled,
            doNotTrack: nav.doNotTrack,
            timezone: new Date().getTimezoneOffset(),
            screen: {
                width: window.screen.width,
                height: window.screen.height,
                depth: window.screen.colorDepth,
                availWidth: window.screen.availWidth,
                availHeight: window.screen.availHeight
            }
        };
        
        // Try to get CPU info
        if (nav.hardwareConcurrency) {
            fingerprintData.cpuCores = nav.hardwareConcurrency;
        }
        
        // Try to get memory info
        if (nav.deviceMemory) {
            fingerprintData.deviceMemory = nav.deviceMemory;
        }
        
        // Try to get connection info
        if (nav.connection) {
            fingerprintData.connection = {
                type: nav.connection.effectiveType,
                downlink: nav.connection.downlink,
                rtt: nav.connection.rtt,
                saveData: nav.connection.saveData
            };
        }
        
        return fingerprintData;
    }
    
    // Get canvas fingerprint
    function getCanvasFingerprint() {
        try {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            
            // Set canvas dimensions
            canvas.width = 200;
            canvas.height = 50;
            
            // Draw text with specific styling
            ctx.textBaseline = 'top';
            ctx.font = '14px Arial';
            ctx.fillStyle = '#f60';
            ctx.fillRect(10, 10, 100, 30);
            ctx.fillStyle = '#069';
            ctx.fillText('Canvas Fingerprint', 2, 15);
            ctx.fillStyle = 'rgba(102, 204, 0, 0.7)';
            ctx.fillText('Canvas Fingerprint', 4, 17);
            
            // Get data URL and extract hash
            const dataURL = canvas.toDataURL();
            fingerprintData.canvas = simpleHash(dataURL);
            
            return fingerprintData.canvas;
        } catch (e) {
            fingerprintData.canvas = 'not-supported';
            return 'not-supported';
        }
    }
    
    // Get WebGL fingerprint
    function getWebGLFingerprint() {
        try {
            const canvas = document.createElement('canvas');
            const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
            
            if (!gl) {
                fingerprintData.webgl = 'not-supported';
                return 'not-supported';
            }
            
            // Collect WebGL info
            const info = {
                vendor: gl.getParameter(gl.VENDOR),
                renderer: gl.getParameter(gl.RENDERER),
                version: gl.getParameter(gl.VERSION),
                shadingLanguageVersion: gl.getParameter(gl.SHADING_LANGUAGE_VERSION),
                extensions: gl.getSupportedExtensions()
            };
            
            // Hash the WebGL info
            fingerprintData.webgl = simpleHash(JSON.stringify(info));
            
            return fingerprintData.webgl;
        } catch (e) {
            fingerprintData.webgl = 'not-supported';
            return 'not-supported';
        }
    }
    
    // Get audio fingerprint
    function getAudioFingerprint() {
        try {
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const analyser = audioContext.createAnalyser();
            
            // Get audio parameters
            fingerprintData.audio = {
                sampleRate: audioContext.sampleRate,
                channelCount: analyser.channelCount,
                fftSize: analyser.fftSize,
                maxDecibels: analyser.maxDecibels,
                minDecibels: analyser.minDecibels
            };
            
            // Hash the audio info
            fingerprintData.audio = simpleHash(JSON.stringify(fingerprintData.audio));
            
            // Close the audio context
            if (audioContext.close) {
                audioContext.close();
            }
            
            return fingerprintData.audio;
        } catch (e) {
            fingerprintData.audio = 'not-supported';
            return 'not-supported';
        }
    }
    
    // Simple hash function
    function simpleHash(str) {
        let hash = 0;
        if (str.length === 0) return hash.toString(36);
        
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32bit integer
        }
        
        // Convert to alphanumeric string (base 36)
        return Math.abs(hash).toString(36);
    }
    
    // Format fingerprint into a consistent format
    function formatFingerprint() {
        // Determine OS type and architecture
        const ua = fingerprintData.userAgent.toLowerCase();
        let osType = 'Oth';
        
        if (ua.includes('win')) osType = 'Win';
        else if (ua.includes('mac')) osType = 'Mac';
        else if (ua.includes('linux')) osType = 'Lnx';
        else if (ua.includes('android')) osType = 'And';
        else if (ua.includes('ios') || ua.includes('iphone') || ua.includes('ipad')) osType = 'iOS';
        
        // Determine architecture (32 or 64 bit)
        const bits = ua.includes('x86_64') || ua.includes('x64') || ua.includes('wow64') ? '64' : '32';
        
        // Generate 5 hash segments
        const segments = [
            simpleHash(fingerprintData.userAgent || '').substring(0, 3),
            simpleHash(JSON.stringify(fingerprintData.screen) || '').substring(0, 3),
            simpleHash(fingerprintData.canvas || '').substring(0, 3),
            simpleHash(fingerprintData.webgl || '').substring(0, 3),
            simpleHash(fingerprintData.audio || '').substring(0, 3)
        ];
        
        // Format: OS+bits|segment1|segment2|segment3|segment4|segment5
        return `${osType}${bits}|${segments.join('|')}`;
    }
    
    // Generate the fingerprint
    function generateFingerprint() {
        collectBasicInfo();
        getCanvasFingerprint();
        getWebGLFingerprint();
        getAudioFingerprint();
        
        // Format the fingerprint
        const formattedFingerprint = formatFingerprint();
        
        // Store the raw data and formatted fingerprint
        fingerprintData.fp = formattedFingerprint;
        
        return {
            raw: fingerprintData,
            fp: formattedFingerprint
        };
    }
    
    // Expose the fingerprinting API
    window.DeviceFingerprint = {
        generate: generateFingerprint,
        getData: function() {
            return fingerprintData;
        }
    };
})();
