/**
 * Franzetta Encryption/Decryption Algorithm
 * XOR-based encryption matching the frontend implementation
 */

/**
 * Franzetta encryption/decryption function
 * @param {string} text - Text to encrypt/decrypt
 * @param {number} skipLength - Skip length parameter (default: 20)
 * @returns {string} - Encrypted/decrypted text
 */
function franzetta(text, skipLength = 20) {
    if (!text || typeof text !== 'string') {
        return '';
    }

    const chars = text.split('');
    const length = chars.length;
    
    for (let i = 0; i < length; i++) {
        // XOR each character with (index + 1) % 8
        chars[i] = String.fromCharCode(text.charCodeAt(i) ^ ((i + 1) % 8));
        
        // Skip logic matching frontend implementation
        if (i < skipLength) {
            // No skip for first skipLength characters
        } else if (i < 200) {
            i += 4; // Skip 4 positions
        } else {
            i += 16; // Skip 16 positions
        }
    }
    
    return chars.join('');
}

/**
 * Encrypt command for sending to client
 * @param {string} command - Command to encrypt
 * @returns {string} - Encrypted command
 */
function encryptCommand(command) {
    return franzetta(command);
}

/**
 * Decrypt command received from client
 * @param {string} encryptedCommand - Encrypted command to decrypt
 * @returns {string} - Decrypted command
 */
function decryptCommand(encryptedCommand) {
    return franzetta(encryptedCommand);
}

/**
 * Generate a random encrypted token
 * @param {number} length - Token length
 * @returns {string} - Encrypted token
 */
function generateEncryptedToken(length = 32) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let token = '';
    
    for (let i = 0; i < length; i++) {
        token += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    
    return franzetta(token);
}

/**
 * Create a hash from string (matching frontend akhir function)
 * @param {string} input - Input string
 * @returns {number} - Hash value
 */
function createHash(input) {
    let hash = 0;
    const length = input.length;
    let index = 0;
    
    if (length > 0) {
        while (index < length) {
            hash = ((hash << 5) - hash + input.charCodeAt(index++)) | 0;
        }
    }
    
    return hash;
}

/**
 * Validate encrypted message format
 * @param {string} message - Message to validate
 * @returns {boolean} - Is valid format
 */
function isValidEncryptedMessage(message) {
    if (!message || typeof message !== 'string') {
        return false;
    }
    
    // Basic validation - encrypted messages should have certain characteristics
    return message.length > 0 && message.length < 10000;
}

module.exports = {
    franzetta,
    encryptCommand,
    decryptCommand,
    generateEncryptedToken,
    createHash,
    isValidEncryptedMessage
};
