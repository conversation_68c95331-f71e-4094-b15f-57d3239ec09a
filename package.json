{"name": "schat-backend", "version": "1.0.0", "description": "Real-time chat system backend with WebSocket support", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["chat", "websocket", "socket.io", "mongodb", "real-time"], "author": "", "license": "ISC", "dependencies": {"bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.3", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "sharp": "^0.33.1", "socket.io": "^4.7.4", "uuid": "^9.0.1", "ws": "^8.18.2"}, "devDependencies": {"nodemon": "^3.0.2"}, "engines": {"node": ">=16.0.0"}}