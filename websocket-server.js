const express = require('express');
const http = require('http');
const WebSocket = require('ws');
const path = require('path');
const url = require('url');

console.log('🚀 Starting WebSocket chat server...');
console.log('📅 Time:', new Date().toISOString());

const app = express();
const server = http.createServer(app);

// Basic middleware
app.use(express.static(path.join(__dirname, 'public')));
app.use(express.json());

// Add request logging
app.use((req, res, next) => {
    console.log(`📡 HTTP ${req.method} ${req.url} from ${req.ip}`);
    next();
});

console.log('✅ Middleware configured');

// Simple franzetta encryption function
function franzetta(text) {
    if (!text || typeof text !== 'string') {
        return '';
    }
    
    let result = '';
    for (let i = 0; i < text.length; i++) {
        result += String.fromCharCode(text.charCodeAt(i) ^ ((i + 1) % 8));
    }
    
    return result;
}

function encryptCommand(command) {
    const encrypted = franzetta(command);
    console.log(`🔐 Encrypting: "${command}" -> "${encrypted}"`);
    return encrypted;
}

function decryptCommand(encryptedCommand) {
    const decrypted = franzetta(encryptedCommand);
    console.log(`🔓 Decrypting: "${encryptedCommand}" -> "${decrypted}"`);
    return decrypted;
}

console.log('✅ Encryption functions ready');

// Simple in-memory storage
const connectedUsers = new Map();
const userSockets = new Map();
const rooms = new Map();

// Initialize default rooms
rooms.set('general', {
    id: 'general',
    topic: 'الغرفة العامة',
    users: 0,
    uco: 0,
    lupd: Date.now(),
    isActive: true
});

rooms.set('welcome', {
    id: 'welcome',
    topic: 'غرفة الترحيب',
    users: 0,
    uco: 0,
    lupd: Date.now(),
    isActive: true
});

console.log('✅ Default rooms created:', Array.from(rooms.keys()));

// API Routes
app.get('/api/health', (req, res) => {
    const health = {
        status: 'ok',
        timestamp: new Date().toISOString(),
        connections: wss.clients.size,
        users: connectedUsers.size,
        rooms: rooms.size,
        uptime: process.uptime()
    };
    console.log('🏥 Health check requested:', health);
    res.json(health);
});

app.get('/', (req, res) => {
    console.log('🏠 Serving main page');
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

console.log('✅ Routes configured');

// WebSocket Server setup
const wss = new WebSocket.Server({
    server: server,
    path: '/socket.io/',
    verifyClient: (info) => {
        const query = url.parse(info.req.url, true).query;
        console.log('🔍 WebSocket connection attempt:', {
            origin: info.origin,
            query: query,
            userAgent: info.req.headers['user-agent']
        });
        
        // Accept connections that match the frontend pattern
        return query.EIO === '4' && query.transport === 'websocket';
    }
});

console.log('✅ WebSocket server configured');

// WebSocket connection handling
wss.on('connection', (ws, req) => {
    const query = url.parse(req.url, true).query;
    console.log(`🔌 NEW WEBSOCKET CONNECTION`);
    console.log(`📊 Total connections: ${wss.clients.size}`);
    console.log(`🌐 Connection info:`, {
        query: query,
        userAgent: req.headers['user-agent'],
        origin: req.headers.origin
    });
    
    let user = null;
    let isAuthenticated = false;
    let connectionId = `ws_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // Send initial Socket.IO-like handshake
    setTimeout(() => {
        console.log('📤 Sending initial handshake...');
        ws.send('0{"sid":"' + connectionId + '","upgrades":[],"pingInterval":25000,"pingTimeout":20000}');

        // Also send a connect message
        setTimeout(() => {
            console.log('📤 Sending connect message...');
            ws.send('40');

            // Send the initial "hi" message that the frontend expects
            setTimeout(() => {
                console.log('📤 Sending initial hi message...');
                const hiMsg = {
                    cmd: 'hi',
                    data: 'server_hello_' + Date.now()
                };
                ws.send('2' + JSON.stringify(hiMsg));
            }, 300);
        }, 200);
    }, 100);
    
    // Handle messages
    ws.on('message', (data) => {
        console.log(`📨 RAW MESSAGE:`, data.toString());
        
        try {
            const message = data.toString();
            
            // Handle Socket.IO protocol messages
            if (message.startsWith('2')) {
                // Socket.IO message type 2 (message)
                const jsonData = message.substring(1);
                console.log(`📝 Parsing Socket.IO message: ${jsonData}`);
                
                const parsed = JSON.parse(jsonData);
                console.log(`📋 Parsed message:`, parsed);
                
                const { cmd, data: messageData } = parsed;
                console.log(`🎯 Command received: "${cmd}"`);
                
                // Decrypt command
                const decryptedCmd = decryptCommand(cmd);
                console.log(`🔓 Decrypted command: "${decryptedCmd}"`);
                
                switch (decryptedCmd) {
                    case 'hi':
                        console.log('🤝 Processing handshake...');
                        const response = franzetta(messageData || 'hello');
                        const responseMsg = {
                            cmd: encryptCommand('hi'),
                            data: response
                        };
                        console.log('📤 Sending handshake response:', responseMsg);
                        ws.send('2' + JSON.stringify(responseMsg));
                        console.log('✅ Handshake completed');
                        break;
                        
                    case 'ping':
                        console.log('🏓 Processing ping...');
                        const pongMsg = {
                            cmd: encryptCommand('pong'),
                            data: messageData
                        };
                        console.log('📤 Sending pong:', pongMsg);
                        ws.send('2' + JSON.stringify(pongMsg));
                        console.log('✅ Pong sent');
                        break;
                        
                    case 'online':
                        console.log('👤 Processing user login...');
                        console.log('📋 Login data:', messageData);
                        
                        const userId = `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
                        user = {
                            id: userId,
                            topic: `زائر_${Math.floor(Math.random() * 9999)}`,
                            rep: 0,
                            pic: 'pic.webp',
                            msg: '',
                            ico: '',
                            roomid: null,
                            v: 0,
                            bg: '#ffffff',
                            ucol: '#000000',
                            s: false,
                            co: '--',
                            lupd: Date.now(),
                            socketId: connectionId,
                            isOnline: true
                        };
                        
                        connectedUsers.set(connectionId, user);
                        userSockets.set(userId, connectionId);
                        isAuthenticated = true;
                        
                        console.log(`👤 User created:`, user);
                        
                        // Send success response
                        const okMsg = {
                            cmd: encryptCommand('ok'),
                            data: { k: 'demo_token_' + userId }
                        };
                        console.log('📤 Sending OK response:', okMsg);
                        ws.send('2' + JSON.stringify(okMsg));
                        
                        console.log(`✅ User ${user.topic} logged in successfully`);
                        
                        // Send initial data after a delay
                        setTimeout(() => {
                            try {
                                console.log('📋 Sending initial data...');
                                
                                // Send user list
                                const userList = Array.from(connectedUsers.values());
                                const ulistMsg = {
                                    cmd: encryptCommand('ulist'),
                                    data: userList
                                };
                                console.log('📤 Sending user list:', ulistMsg);
                                ws.send('2' + JSON.stringify(ulistMsg));
                                
                                // Send room list
                                const roomList = Array.from(rooms.values());
                                const rlistMsg = {
                                    cmd: encryptCommand('rlist'),
                                    data: roomList
                                };
                                console.log('📤 Sending room list:', rlistMsg);
                                ws.send('2' + JSON.stringify(rlistMsg));
                                
                                // Send empty arrays for other data
                                const emosMsg = {
                                    cmd: encryptCommand('emos'),
                                    data: []
                                };
                                ws.send('2' + JSON.stringify(emosMsg));
                                
                                const dro3Msg = {
                                    cmd: encryptCommand('dro3'),
                                    data: []
                                };
                                ws.send('2' + JSON.stringify(dro3Msg));
                                
                                console.log('✅ Initial data sent successfully');
                            } catch (error) {
                                console.error('❌ Error sending initial data:', error);
                            }
                        }, 1000);
                        break;
                        
                    default:
                        console.log(`❓ Unknown command: "${decryptedCmd}"`);
                        break;
                }
            } else if (message === '3') {
                // Socket.IO ping
                console.log('🏓 Socket.IO ping received, sending pong');
                ws.send('3'); // pong
            } else {
                console.log(`❓ Unknown message format: ${message}`);
            }
        } catch (error) {
            console.error('❌ Error handling message:', error);
            console.error('❌ Stack trace:', error.stack);
        }
    });
    
    // Handle disconnection
    ws.on('close', (code, reason) => {
        console.log(`🔌 WEBSOCKET DISCONNECTION: ${connectionId} - Code: ${code}, Reason: ${reason}`);
        
        if (user) {
            connectedUsers.delete(connectionId);
            userSockets.delete(user.id);
            console.log(`👋 User ${user.topic} disconnected`);
        }
        
        console.log(`📊 Remaining connections: ${wss.clients.size}`);
    });
    
    // Handle errors
    ws.on('error', (error) => {
        console.error(`❌ WEBSOCKET ERROR for ${connectionId}:`, error);
    });
    
    // Send periodic pings
    const pingInterval = setInterval(() => {
        if (ws.readyState === WebSocket.OPEN) {
            ws.send('2'); // Socket.IO ping
        } else {
            clearInterval(pingInterval);
        }
    }, 25000);
});

console.log('✅ WebSocket handlers configured');

// Start server
const PORT = process.env.PORT || 3002;
server.listen(PORT, () => {
    console.log(`🚀 WebSocket chat server running on port ${PORT}`);
    console.log(`🌐 Open http://localhost:${PORT} in your browser`);
    console.log(`📊 Health check: http://localhost:${PORT}/api/health`);
    console.log('⏰ Server started at:', new Date().toISOString());
});

// Log any unhandled errors
process.on('uncaughtException', (error) => {
    console.error('💥 UNCAUGHT EXCEPTION:', error);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('💥 UNHANDLED REJECTION:', reason);
});

console.log('✅ Server setup complete - Ready for WebSocket connections!');
