require('dotenv').config();

const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const mongoose = require('mongoose');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const path = require('path');
const cron = require('node-cron');

// Import utilities and models
const { franzetta, encryptCommand, decryptCommand, generateEncryptedToken } = require('./utils/encryption');
const User = require('./models/User');
const Room = require('./models/Room');
const Message = require('./models/Message');
const AdminLog = require('./models/AdminLog');

// Import controllers
const SocketController = require('./controllers/SocketController');
const AdminController = require('./controllers/AdminController');

const app = express();
const server = http.createServer(app);

// Socket.IO configuration
const io = socketIo(server, {
    cors: {
        origin: process.env.CORS_ORIGIN || "*",
        methods: ["GET", "POST"],
        credentials: true
    },
    transports: ['websocket'],
    pingTimeout: 30000,
    pingInterval: 10000,
    maxHttpBufferSize: 1e6, // 1MB for audio data
    allowEIO3: true // Support EIO=4 from frontend
});

// Middleware
app.use(helmet({
    contentSecurityPolicy: false,
    crossOriginEmbedderPolicy: false
}));

app.use(compression());

app.use(cors({
    origin: process.env.CORS_ORIGIN || "*",
    credentials: true
}));

// Rate limiting
const limiter = rateLimit({
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000,
    max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100,
    message: 'Too many requests from this IP',
    standardHeaders: true,
    legacyHeaders: false
});

app.use('/api/', limiter);

app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Serve static files
app.use(express.static(path.join(__dirname, 'public')));

// Database connection
mongoose.connect(process.env.MONGODB_URI, {
    useNewUrlParser: true,
    useUnifiedTopology: true,
    maxPoolSize: 10,
    serverSelectionTimeoutMS: 5000,
    socketTimeoutMS: 45000,
})
.then(() => {
    console.log('✅ Connected to MongoDB');
    initializeDefaultData();
})
.catch(err => {
    console.error('❌ MongoDB connection error:', err);
    process.exit(1);
});

// Initialize default data
async function initializeDefaultData() {
    try {
        // Create default rooms if they don't exist
        const defaultRooms = [
            {
                id: 'general',
                topic: 'الغرفة العامة',
                createdBy: 'system',
                category: 'general',
                isActive: true
            },
            {
                id: 'welcome',
                topic: 'غرفة الترحيب',
                createdBy: 'system',
                category: 'general',
                isActive: true
            }
        ];

        for (const roomData of defaultRooms) {
            const existingRoom = await Room.findOne({ id: roomData.id });
            if (!existingRoom) {
                await Room.create(roomData);
                console.log(`✅ Created default room: ${roomData.topic}`);
            }
        }
    } catch (error) {
        console.error('❌ Error initializing default data:', error);
    }
}

// Global variables for tracking
const connectedUsers = new Map(); // socketId -> user data
const userSockets = new Map(); // userId -> socketId
const roomUsers = new Map(); // roomId -> Set of userIds
const voiceStreams = new Map(); // userId -> voice stream data

// Socket.IO connection handling
io.on('connection', (socket) => {
    console.log(`🔌 New connection: ${socket.id}`);
    
    // Initialize socket controller
    const socketController = new SocketController(socket, io, {
        connectedUsers,
        userSockets,
        roomUsers,
        voiceStreams
    });
    
    // Handle initial handshake
    socket.on('message', async (data) => {
        try {
            if (typeof data === 'string') {
                const parsed = JSON.parse(data);
                await socketController.handleMessage(parsed);
            }
        } catch (error) {
            console.error('❌ Error handling message:', error);
            socket.emit('error', { message: 'Invalid message format' });
        }
    });
    
    // Handle binary data (audio)
    socket.on('binary', async (data) => {
        try {
            await socketController.handleBinaryData(data);
        } catch (error) {
            console.error('❌ Error handling binary data:', error);
        }
    });
    
    // Handle disconnection
    socket.on('disconnect', async (reason) => {
        console.log(`🔌 Disconnection: ${socket.id} - ${reason}`);
        await socketController.handleDisconnect(reason);
    });
    
    // Handle connection errors
    socket.on('error', (error) => {
        console.error(`❌ Socket error for ${socket.id}:`, error);
    });
});

// API Routes
app.get('/api/health', (req, res) => {
    res.json({
        status: 'ok',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        connections: io.engine.clientsCount
    });
});

app.get('/api/stats', async (req, res) => {
    try {
        const stats = {
            users: {
                total: await User.countDocuments(),
                online: await User.countDocuments({ isOnline: true }),
                banned: await User.countDocuments({ isBanned: true })
            },
            rooms: {
                total: await Room.countDocuments(),
                active: await Room.countDocuments({ isActive: true }),
                private: await Room.countDocuments({ isPrivate: true })
            },
            messages: {
                total: await Message.countDocuments(),
                today: await Message.countDocuments({
                    createdAt: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) }
                })
            },
            server: {
                uptime: process.uptime(),
                memory: process.memoryUsage(),
                connections: io.engine.clientsCount
            }
        };
        
        res.json(stats);
    } catch (error) {
        console.error('❌ Error getting stats:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// Admin routes (will be created later)
// app.use('/api/admin', require('./routes/admin'));

// Serve main page
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

app.get('/cp', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Cleanup tasks
cron.schedule('*/30 * * * *', async () => {
    try {
        console.log('🧹 Running cleanup tasks...');
        
        // Cleanup inactive users
        const inactiveTimeout = parseInt(process.env.INACTIVE_USER_TIMEOUT_MINUTES) || 60;
        await User.cleanupInactiveUsers(inactiveTimeout);
        
        // Cleanup empty rooms
        await Room.cleanupEmptyRooms();
        
        // Cleanup old messages
        const messageRetentionDays = parseInt(process.env.OLD_MESSAGE_CLEANUP_DAYS) || 30;
        await Message.cleanupOldMessages(messageRetentionDays);
        
        // Cleanup old admin logs
        await AdminLog.cleanupOldLogs(90);
        
        console.log('✅ Cleanup tasks completed');
    } catch (error) {
        console.error('❌ Error during cleanup:', error);
    }
});

// Error handling
process.on('uncaughtException', (error) => {
    console.error('❌ Uncaught Exception:', error);
    process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
    process.exit(1);
});

// Graceful shutdown
process.on('SIGTERM', async () => {
    console.log('🛑 SIGTERM received, shutting down gracefully...');
    
    // Close server
    server.close(() => {
        console.log('✅ HTTP server closed');
        
        // Close database connection
        mongoose.connection.close(false, () => {
            console.log('✅ MongoDB connection closed');
            process.exit(0);
        });
    });
});

// Start server
const PORT = process.env.PORT || 3000;
server.listen(PORT, () => {
    console.log(`🚀 Server running on port ${PORT}`);
    console.log(`🌐 Environment: ${process.env.NODE_ENV || 'development'}`);
    console.log(`📊 Admin panel: http://localhost:${PORT}/cp`);
});
