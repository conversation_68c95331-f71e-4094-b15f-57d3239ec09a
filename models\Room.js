const mongoose = require('mongoose');

const roomSchema = new mongoose.Schema({
    // Basic room information
    id: {
        type: String,
        required: true,
        unique: true,
        index: true
    },
    topic: {
        type: String,
        required: true,
        maxlength: 100,
        trim: true
    },
    users: {
        type: Number,
        default: 0,
        min: 0
    },
    uco: {
        type: Number,
        default: 0, // User count online
        min: 0
    },
    lupd: {
        type: Date,
        default: Date.now,
        index: true
    },
    
    // Room settings
    pic: {
        type: String,
        default: 'room.webp'
    },
    bg: {
        type: String,
        default: '#ffffff'
    },
    welcome: {
        type: String,
        maxlength: 500,
        default: ''
    },
    
    // Room permissions and settings
    isPrivate: {
        type: Boolean,
        default: false
    },
    password: {
        type: String,
        default: null,
        select: false
    },
    maxUsers: {
        type: Number,
        default: 50,
        min: 1,
        max: 200
    },
    
    // Moderation
    ops: [{
        type: String // User IDs of operators
    }],
    banned: [{
        userId: String,
        reason: String,
        bannedBy: String,
        bannedAt: {
            type: Date,
            default: Date.now
        },
        expires: Date
    }],
    
    // Room status
    isActive: {
        type: Boolean,
        default: true,
        index: true
    },
    isFrozen: {
        type: Boolean,
        default: false
    },
    
    // Voice chat settings
    voiceEnabled: {
        type: Boolean,
        default: true
    },
    maxVoiceUsers: {
        type: Number,
        default: 5,
        min: 1,
        max: 10
    },
    
    // Statistics
    totalMessages: {
        type: Number,
        default: 0
    },
    totalUsers: {
        type: Number,
        default: 0
    },
    
    // Creation info
    createdBy: {
        type: String,
        required: true
    },
    category: {
        type: String,
        enum: ['general', 'gaming', 'music', 'tech', 'social', 'private'],
        default: 'general'
    },
    
    // Room rules and description
    rules: [{
        rule: String,
        addedBy: String,
        addedAt: {
            type: Date,
            default: Date.now
        }
    }],
    description: {
        type: String,
        maxlength: 1000,
        default: ''
    },
    
    // Auto-moderation settings
    autoMod: {
        enabled: {
            type: Boolean,
            default: false
        },
        badWords: [String],
        maxMessageLength: {
            type: Number,
            default: 500
        },
        slowMode: {
            enabled: {
                type: Boolean,
                default: false
            },
            seconds: {
                type: Number,
                default: 5
            }
        }
    }
}, {
    timestamps: true,
    toJSON: {
        transform: function(doc, ret) {
            delete ret._id;
            delete ret.__v;
            delete ret.password;
            return ret;
        }
    }
});

// Indexes for performance
roomSchema.index({ isActive: 1, uco: -1 });
roomSchema.index({ lupd: -1 });
roomSchema.index({ category: 1, isActive: 1 });
roomSchema.index({ 'ops': 1 });

// Methods
roomSchema.methods.toClientFormat = function() {
    return {
        id: this.id,
        topic: this.topic,
        users: this.users,
        uco: this.uco,
        lupd: this.lupd.getTime(),
        pic: this.pic,
        bg: this.bg,
        isPrivate: this.isPrivate,
        maxUsers: this.maxUsers,
        voiceEnabled: this.voiceEnabled,
        category: this.category
    };
};

roomSchema.methods.updateActivity = function() {
    this.lupd = new Date();
    return this.save();
};

roomSchema.methods.addUser = function() {
    this.users = Math.max(0, this.users + 1);
    this.uco = Math.max(0, this.uco + 1);
    this.totalUsers = Math.max(this.totalUsers, this.users);
    this.lupd = new Date();
    return this.save();
};

roomSchema.methods.removeUser = function() {
    this.users = Math.max(0, this.users - 1);
    this.uco = Math.max(0, this.uco - 1);
    this.lupd = new Date();
    return this.save();
};

roomSchema.methods.isUserBanned = function(userId) {
    const now = new Date();
    return this.banned.some(ban => 
        ban.userId === userId && 
        (!ban.expires || ban.expires > now)
    );
};

roomSchema.methods.isUserOp = function(userId) {
    return this.ops.includes(userId);
};

// Static methods
roomSchema.statics.findActiveRooms = function() {
    return this.find({ isActive: true })
        .sort({ uco: -1, lupd: -1 })
        .limit(100);
};

roomSchema.statics.findPopularRooms = function(limit = 20) {
    return this.find({ isActive: true, isPrivate: false })
        .sort({ uco: -1, users: -1 })
        .limit(limit);
};

roomSchema.statics.cleanupEmptyRooms = function() {
    return this.updateMany(
        { users: 0, uco: 0, createdAt: { $lt: new Date(Date.now() - 24 * 60 * 60 * 1000) } },
        { $set: { isActive: false } }
    );
};

module.exports = mongoose.model('Room', roomSchema);
