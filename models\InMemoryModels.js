// In-memory models for testing without MongoDB
const { v4: uuidv4 } = require('uuid');

// In-memory storage
const users = new Map();
const rooms = new Map();
const messages = new Map();
const adminLogs = new Map();

// User model
class InMemoryUser {
    constructor(data) {
        this.id = data.id || uuidv4();
        this.topic = data.topic || `زائر_${Math.floor(Math.random() * 9999)}`;
        this.rep = data.rep || 0;
        this.pic = data.pic || 'pic.webp';
        this.msg = data.msg || '';
        this.ico = data.ico || '';
        this.roomid = data.roomid || null;
        this.v = data.v || 0;
        this.bg = data.bg || '#ffffff';
        this.ucol = data.ucol || '#000000';
        this.socketId = data.socketId || null;
        this.isOnline = data.isOnline || false;
        this.lastSeen = data.lastSeen || new Date();
        this.s = data.s || false;
        this.co = data.co || '--';
        this.powers = data.powers || [];
        this.lupd = data.lupd || new Date();
        this.joinTime = data.joinTime || new Date();
        this.micEnabled = data.micEnabled || false;
        this.isMuted = data.isMuted || false;
        this.fingerprint = data.fingerprint || null;
        this.isBanned = data.isBanned || false;
        this.messageCount = data.messageCount || 0;
        this.createdAt = data.createdAt || new Date();
        this.updatedAt = data.updatedAt || new Date();
    }

    toClientFormat() {
        return {
            id: this.id,
            topic: this.topic,
            rep: this.rep,
            pic: this.pic,
            msg: this.msg,
            ico: this.ico,
            roomid: this.roomid,
            v: this.v,
            bg: this.bg,
            ucol: this.ucol,
            s: this.s,
            co: this.co,
            lupd: this.lupd.getTime()
        };
    }

    updateActivity() {
        this.lupd = new Date();
        this.lastSeen = new Date();
        this.updatedAt = new Date();
        users.set(this.id, this);
        return Promise.resolve(this);
    }

    save() {
        this.updatedAt = new Date();
        users.set(this.id, this);
        return Promise.resolve(this);
    }

    static create(data) {
        const user = new InMemoryUser(data);
        users.set(user.id, user);
        return Promise.resolve(user);
    }

    static findOne(query) {
        for (const user of users.values()) {
            if (query.id && user.id === query.id) {
                return Promise.resolve(user);
            }
            if (query.socketId && user.socketId === query.socketId) {
                return Promise.resolve(user);
            }
        }
        return Promise.resolve(null);
    }

    static find(query = {}) {
        const result = [];
        for (const user of users.values()) {
            let matches = true;
            
            if (query.isOnline !== undefined && user.isOnline !== query.isOnline) {
                matches = false;
            }
            if (query.isBanned !== undefined && user.isBanned !== query.isBanned) {
                matches = false;
            }
            if (query.roomid !== undefined && user.roomid !== query.roomid) {
                matches = false;
            }
            
            if (matches) {
                result.push(user);
            }
        }
        return Promise.resolve(result);
    }

    static findOnlineUsers(roomId = null) {
        const query = { isOnline: true, isBanned: false };
        if (roomId) {
            query.roomid = roomId;
        }
        return this.find(query);
    }

    static countDocuments(query = {}) {
        return this.find(query).then(results => results.length);
    }

    static cleanupInactiveUsers(timeoutMinutes = 60) {
        const cutoffTime = new Date(Date.now() - timeoutMinutes * 60 * 1000);
        let count = 0;
        
        for (const user of users.values()) {
            if (user.lastSeen < cutoffTime && user.isOnline) {
                user.isOnline = false;
                user.socketId = null;
                user.roomid = null;
                count++;
            }
        }
        
        return Promise.resolve({ modifiedCount: count });
    }
}

// Room model
class InMemoryRoom {
    constructor(data) {
        this.id = data.id || uuidv4();
        this.topic = data.topic || 'غرفة جديدة';
        this.users = data.users || 0;
        this.uco = data.uco || 0;
        this.lupd = data.lupd || new Date();
        this.pic = data.pic || 'room.webp';
        this.bg = data.bg || '#ffffff';
        this.welcome = data.welcome || '';
        this.isPrivate = data.isPrivate || false;
        this.maxUsers = data.maxUsers || 50;
        this.ops = data.ops || [];
        this.banned = data.banned || [];
        this.isActive = data.isActive !== undefined ? data.isActive : true;
        this.isFrozen = data.isFrozen || false;
        this.voiceEnabled = data.voiceEnabled !== undefined ? data.voiceEnabled : true;
        this.maxVoiceUsers = data.maxVoiceUsers || 5;
        this.totalMessages = data.totalMessages || 0;
        this.totalUsers = data.totalUsers || 0;
        this.createdBy = data.createdBy || 'system';
        this.category = data.category || 'general';
        this.createdAt = data.createdAt || new Date();
        this.updatedAt = data.updatedAt || new Date();
    }

    toClientFormat() {
        return {
            id: this.id,
            topic: this.topic,
            users: this.users,
            uco: this.uco,
            lupd: this.lupd.getTime(),
            pic: this.pic,
            bg: this.bg,
            isPrivate: this.isPrivate,
            maxUsers: this.maxUsers,
            voiceEnabled: this.voiceEnabled,
            category: this.category
        };
    }

    updateActivity() {
        this.lupd = new Date();
        this.updatedAt = new Date();
        rooms.set(this.id, this);
        return Promise.resolve(this);
    }

    addUser() {
        this.users = Math.max(0, this.users + 1);
        this.uco = Math.max(0, this.uco + 1);
        this.totalUsers = Math.max(this.totalUsers, this.users);
        this.lupd = new Date();
        this.updatedAt = new Date();
        rooms.set(this.id, this);
        return Promise.resolve(this);
    }

    removeUser() {
        this.users = Math.max(0, this.users - 1);
        this.uco = Math.max(0, this.uco - 1);
        this.lupd = new Date();
        this.updatedAt = new Date();
        rooms.set(this.id, this);
        return Promise.resolve(this);
    }

    isUserBanned(userId) {
        const now = new Date();
        return this.banned.some(ban => 
            ban.userId === userId && 
            (!ban.expires || ban.expires > now)
        );
    }

    isUserOp(userId) {
        return this.ops.includes(userId);
    }

    save() {
        this.updatedAt = new Date();
        rooms.set(this.id, this);
        return Promise.resolve(this);
    }

    static create(data) {
        const room = new InMemoryRoom(data);
        rooms.set(room.id, room);
        return Promise.resolve(room);
    }

    static findOne(query) {
        for (const room of rooms.values()) {
            if (query.id && room.id === query.id) {
                if (query.isActive === undefined || room.isActive === query.isActive) {
                    return Promise.resolve(room);
                }
            }
        }
        return Promise.resolve(null);
    }

    static find(query = {}) {
        const result = [];
        for (const room of rooms.values()) {
            let matches = true;
            
            if (query.isActive !== undefined && room.isActive !== query.isActive) {
                matches = false;
            }
            if (query.isPrivate !== undefined && room.isPrivate !== query.isPrivate) {
                matches = false;
            }
            
            if (matches) {
                result.push(room);
            }
        }
        return Promise.resolve(result);
    }

    static findActiveRooms() {
        return this.find({ isActive: true }).then(rooms => 
            rooms.sort((a, b) => b.uco - a.uco || b.lupd - a.lupd).slice(0, 100)
        );
    }

    static countDocuments(query = {}) {
        return this.find(query).then(results => results.length);
    }

    static cleanupEmptyRooms() {
        let count = 0;
        const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
        
        for (const room of rooms.values()) {
            if (room.users === 0 && room.uco === 0 && room.createdAt < oneDayAgo) {
                room.isActive = false;
                count++;
            }
        }
        
        return Promise.resolve({ modifiedCount: count });
    }
}

// Message model
class InMemoryMessage {
    constructor(data) {
        this.id = data.id || uuidv4();
        this.msg = data.msg || '';
        this.link = data.link || null;
        this.bid = data.bid || null;
        this.replyTo = data.replyTo || null;
        this.userId = data.userId || '';
        this.userTopic = data.userTopic || '';
        this.userPic = data.userPic || 'pic.webp';
        this.userBg = data.userBg || '#ffffff';
        this.userColor = data.userColor || '#000000';
        this.userIco = data.userIco || '';
        this.roomId = data.roomId || '';
        this.type = data.type || 'text';
        this.isEdited = data.isEdited || false;
        this.isDeleted = data.isDeleted || false;
        this.isHidden = data.isHidden || false;
        this.isPinned = data.isPinned || false;
        this.isSystem = data.isSystem || false;
        this.reactions = data.reactions || [];
        this.views = data.views || 0;
        this.reports = data.reports || [];
        this.attachments = data.attachments || [];
        this.createdAt = data.createdAt || new Date();
        this.updatedAt = data.updatedAt || new Date();
    }

    toClientFormat() {
        return {
            id: this.id,
            msg: this.isDeleted ? '[رسالة محذوفة]' : this.msg,
            link: this.isDeleted ? null : this.link,
            bid: this.bid,
            userId: this.userId,
            userTopic: this.userTopic,
            userPic: this.userPic,
            userBg: this.userBg,
            userColor: this.userColor,
            userIco: this.userIco,
            roomId: this.roomId,
            type: this.type,
            isEdited: this.isEdited,
            reactions: this.reactions,
            isPinned: this.isPinned,
            createdAt: this.createdAt.getTime(),
            replyTo: this.replyTo,
            attachments: this.isDeleted ? [] : this.attachments
        };
    }

    save() {
        this.updatedAt = new Date();
        messages.set(this.id, this);
        return Promise.resolve(this);
    }

    static create(data) {
        const message = new InMemoryMessage(data);
        messages.set(message.id, message);
        return Promise.resolve(message);
    }

    static findOne(query) {
        for (const message of messages.values()) {
            if (query.id && message.id === query.id) {
                return Promise.resolve(message);
            }
        }
        return Promise.resolve(null);
    }

    static find(query = {}) {
        const result = [];
        for (const message of messages.values()) {
            let matches = true;
            
            if (query.roomId && message.roomId !== query.roomId) {
                matches = false;
            }
            if (query.isDeleted !== undefined && message.isDeleted !== query.isDeleted) {
                matches = false;
            }
            
            if (matches) {
                result.push(message);
            }
        }
        return Promise.resolve(result);
    }

    static findRoomMessages(roomId, limit = 50, before = null) {
        return this.find({ roomId, isDeleted: false }).then(msgs => {
            let filtered = msgs;
            if (before) {
                filtered = msgs.filter(m => m.createdAt < new Date(before));
            }
            return filtered.sort((a, b) => b.createdAt - a.createdAt).slice(0, limit);
        });
    }

    static countDocuments(query = {}) {
        return this.find(query).then(results => results.length);
    }

    static cleanupOldMessages(days = 30) {
        const cutoffDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000);
        let count = 0;
        
        for (const [id, message] of messages) {
            if (message.createdAt < cutoffDate && !message.isPinned && !message.isSystem) {
                messages.delete(id);
                count++;
            }
        }
        
        return Promise.resolve({ deletedCount: count });
    }
}

// AdminLog model (simplified)
class InMemoryAdminLog {
    static logAction(actionData) {
        const log = {
            id: uuidv4(),
            ...actionData,
            createdAt: new Date()
        };
        adminLogs.set(log.id, log);
        return Promise.resolve(log);
    }

    static findRecentActions(hours = 24, limit = 200) {
        const since = new Date(Date.now() - hours * 60 * 60 * 1000);
        const result = [];
        
        for (const log of adminLogs.values()) {
            if (log.createdAt >= since) {
                result.push(log);
            }
        }
        
        return Promise.resolve(result.sort((a, b) => b.createdAt - a.createdAt).slice(0, limit));
    }

    static cleanupOldLogs(days = 90) {
        const cutoffDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000);
        let count = 0;
        
        for (const [id, log] of adminLogs) {
            if (log.createdAt < cutoffDate && !['high', 'critical'].includes(log.severity)) {
                adminLogs.delete(id);
                count++;
            }
        }
        
        return Promise.resolve({ deletedCount: count });
    }
}

module.exports = {
    User: InMemoryUser,
    Room: InMemoryRoom,
    Message: InMemoryMessage,
    AdminLog: InMemoryAdminLog
};
